

from paddleocr import PPStructureV3
import fitz  # PyMuPDF库，用于PDF转图像
import os
import cv2
import numpy as np
from PIL import Image
import json
import re

# 初始化模型（可根据需求启用可选功能，如GPU、方向分类等）
pipeline = PPStructureV3(device="gpu")  # 示例：使用GPU加速（需确保环境支持）

# 输入文件路径（支持PDF或图像）
input_path = "test_images\image.png"
# 临时保存PDF转图像的文件夹（处理完可删除）
temp_img_folder = "temp_pdf_images"
os.makedirs(temp_img_folder, exist_ok=True)
# 输出保存文件夹
save_folder = "output"
os.makedirs(save_folder, exist_ok=True)
# Badcase 可视化保存文件夹
badcase_vis_folder = os.path.join(save_folder, "badcase_vis")
os.makedirs(badcase_vis_folder, exist_ok=True)

# --- Bad Case 检测函数 ---
def detect_bad_cases(region, img_width, img_height):
    """
    检测模型输出的潜在错误区域
    返回: (is_bad_case, error_reason)
    """
    # 获取边界框坐标
    x1, y1, x2, y2 = region["coordinate"]
    
    # 条件1: 边界框超出图像范围
    if x1 < 0 or y1 < 0 or x2 > img_width or y2 > img_height:
        return True, "bbox_out_of_image"
    
    # 条件2: 置信度过低
    if region["score"] < 0.6:
        return True, "low_confidence"
    
    # 条件3: 表格结构不完整
    if region["label"] == "Table":
        html = region.get("html", "")
        # 检测表格标签是否完整
        if "<table>" not in html or "</table>" not in html:
            return True, "incomplete_table"
        # 检测空表格
        if "<tr>" not in html or "<td>" not in html:
            return True, "empty_table"
    
    # 条件4: 文本区域问题
    if region["label"] in ["Text", "Title", "List"]:
        text = region.get("text", "")
        # 检测乱码字符
        if any(char in text for char in ["�", "��", "??", "�", "▯"]):
            return True, "garbled_text"
        # 检测过多换行符
        if "\n\n\n" in text:
            return True, "excessive_line_breaks"
        # 检测空白文本
        if not text.strip():
            return True, "empty_text"
    
    # 条件5: 关键信息缺失
    if region["label"] == "Title" and not region.get("text", "").strip():
        return True, "missing_title"
    
    # 条件6: 区域尺寸异常
    width = x2 - x1
    height = y2 - y1
    # 过小的区域
    if width * height < 100:  # 小于100像素
        return True, "too_small_area"
    # 过于细长的区域（宽高比异常）
    if min(width, height) > 0 and max(width, height) / min(width, height) > 20:
        return True, "abnormal_aspect_ratio"
    
    # 条件7: 公式识别错误
    if region["label"] == "Equation":
        formula = region.get("text", "")
        # 检测空公式
        if not formula.strip():
            return True, "empty_formula"
        # 检测常见公式错误模式
        if any(pattern in formula for pattern in ["\\un", "\\und", "\\def"]):
            return True, "malformed_formula"
    
    # 条件8: 图片区域问题
    if region["label"] == "Figure":
        # 检测图片是否被正确提取
        if not region.get("image", None):
            return True, "missing_image"
    
    # 条件9: 列表项格式错误
    if region["label"] == "List":
        text = region.get("text", "")
        # 检测列表项是否缺少项目符号或编号
        if len(text) > 0 and not any(char in text[:5] for char in ["•", "-", "*", "·", "‣", "→"]):
            # 检查编号格式 (1., a), i. 等)
            if not re.match(r'^\s*(\d+\.|[a-z]\)|[ivx]+\.)', text.lower()):
                return True, "invalid_list_format"
    
    # 条件10: 页眉页脚位置异常
    if region["label"] in ["Header", "Footer"]:
        # 页眉位置在页面下方15%以上
        if region["label"] == "Header" and y1 > img_height * 0.15:
            return True, "misplaced_header_footer"
        # 页脚位置在页面下方85%以下
        if region["label"] == "Footer" and y2 < img_height * 0.85:
            return True, "misplaced_header_footer"
    
    return False, ""

# --- 区域重叠检测函数 ---
def check_region_overlap(regions):
    """
    检测区域之间的重叠冲突
    返回：有重叠的区域列表
    """
    overlapping = []
    for i, region1 in enumerate(regions):
        for j, region2 in enumerate(regions):
            if i >= j:
                continue  # 避免重复比较
                
            x1a, y1a, x2a, y2a = region1["coordinate"]
            x1b, y1b, x2b, y2b = region2["coordinate"]
            
            # 计算重叠面积
            overlap_x = max(0, min(x2a, x2b) - max(x1a, x1b))
            overlap_y = max(0, min(y2a, y2b) - max(y1a, y1b))
            overlap_area = overlap_x * overlap_y
            
            # 计算较小区域的面积
            area1 = (x2a - x1a) * (y2a - y1a)
            area2 = (x2b - x1b) * (y2b - y1b)
            min_area = min(area1, area2)
            
            # 如果重叠面积超过较小区域的50%，视为严重重叠
            if overlap_area > 0 and overlap_area > min_area * 0.5:
                overlapping.append((region1, region2))
    
    return overlapping

# --- 可视化标注函数 ---
def visualize_bad_case(img_path, bad_regions):
    """
    在图像上标注Bad Case区域
    """
    img = cv2.imread(img_path)
    img_height, img_width = img.shape[:2]
    
    # 错误类型颜色映射
    color_map = {
        "bbox_out_of_image": (0, 0, 255),     # 红色
        "low_confidence": (0, 165, 255),      # 橙色
        "incomplete_table": (255, 255, 0),    # 青色
        "garbled_text": (0, 255, 255),        # 黄色
        "region_overlap": (128, 0, 128),      # 紫色
        "empty_table": (255, 0, 255),         # 粉色
        "empty_text": (255, 0, 0),            # 蓝色
        "too_small_area": (0, 100, 0),        # 深绿色
        "abnormal_aspect_ratio": (0, 255, 0), # 绿色
        "malformed_formula": (165, 42, 42),   # 棕色
        "misplaced_header_footer": (128, 128, 128) # 灰色
    }
    
    for region, reason in bad_regions:
        # 将浮点数坐标转换为整数
        x1, y1, x2, y2 = [int(coord) for coord in region["coordinate"]]
        
        # 获取对应错误类型的颜色
        color = color_map.get(reason, (0, 0, 255))  # 默认为红色
        
        # 绘制边界框
        cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
        
        # 绘制错误原因标签
        label = f"{region['label']}: {reason}"
        cv2.putText(img, label, (x1, max(20, y1-10)), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
        
        # 对于文本区域，添加文本预览
        if region["label"] in ["Text", "Title", "List"] and "text" in region:
            sample_text = region["text"][:30] + ("..." if len(region["text"]) > 30 else "")
            cv2.putText(img, f"Text: {sample_text}", (x1, min(img_height-10, y2+20)), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
    
    output_path = os.path.join(badcase_vis_folder, os.path.basename(img_path))
    cv2.imwrite(output_path, img)

# --- 错误统计函数 ---
def generate_error_report(error_stats, total_pages, total_regions):
    """
    生成错误统计报告
    """
    report = "\n===== 错误统计报告 =====\n"
    report += f"处理总页数: {total_pages}\n"
    report += f"检测总区域数: {total_regions}\n"
    report += f"发现错误总数: {sum(error_stats.values())}\n\n"
    
    if error_stats:
        report += "错误类型分布:\n"
        max_len = max(len(reason) for reason in error_stats.keys())
        
        for reason, count in sorted(error_stats.items(), key=lambda x: x[1], reverse=True):
            report += f"  {reason.ljust(max_len)} : {count} ({count/sum(error_stats.values())*100:.1f}%)\n"
    else:
        report += "未检测到任何错误，文档质量良好！\n"
    
    return report

# --- 主处理流程 ---
def main():
    # 错误统计
    error_stats = {}
    total_regions = 0
    processed_images = []
    
    # 步骤1：根据输入类型准备图像
    if input_path.lower().endswith(('.png', '.jpg', '.jpeg')):
        # 直接使用图像文件
        processed_images = [input_path]
    elif input_path.lower().endswith('.pdf'):
        # 将PDF转换为图像
        doc = fitz.open(input_path)
        for page_idx in range(doc.page_count):
            page = doc.load_page(page_idx)
            pix = page.get_pixmap()
            img_path = os.path.join(temp_img_folder, f"page_{page_idx}.png")
            pix.save(img_path)
            processed_images.append(img_path)
    else:
        print(f"错误：不支持的文件类型 - {input_path}")
        return
    
    # 步骤2：处理所有图像
    output = pipeline.predict(processed_images if len(processed_images) > 1 else processed_images[0])
    
    # 步骤3：检测Bad Case并可视化标注
    for img_path in processed_images:
        img = Image.open(img_path)
        img_width, img_height = img.size
        
        # 获取当前图像的预测结果
        current_output = [res for res in output if res["input_path"] == img_path]
        if not current_output:
            continue
        
        regions = current_output[0]["layout_det_res"]["boxes"]
        total_regions += len(regions)
        
        # 检测Bad Case
        bad_regions = []
        for region in regions:
            is_bad, reason = detect_bad_cases(region, img_width, img_height)
            if is_bad:
                bad_regions.append((region, reason))
                error_stats[reason] = error_stats.get(reason, 0) + 1
                print(f"Bad Case 发现: {os.path.basename(img_path)} | 类型: {region['label']} | 原因: {reason}")
        
        # 检测区域重叠
        overlapping_pairs = check_region_overlap(regions)
        for reg1, reg2 in overlapping_pairs:
            bad_regions.append((reg1, "region_overlap"))
            bad_regions.append((reg2, "region_overlap"))
            error_stats["region_overlap"] = error_stats.get("region_overlap", 0) + 2
            print(f"Bad Case 发现: {os.path.basename(img_path)} | 区域重叠: {reg1['label']} 和 {reg2['label']}")
        
        # 可视化标注
        if bad_regions:
            visualize_bad_case(img_path, bad_regions)
    
    # 步骤4：保存结果
    for res in output:
        res.print()  # 打印结构化输出
        res.save_to_json(save_path=save_folder)  # 保存JSON结果
        res.save_to_markdown(save_path=save_folder)  # 保存Markdown结果
    
    # 步骤5：生成错误报告
    report = generate_error_report(error_stats, len(processed_images), total_regions)
    print(report)
    
    # 保存报告到文件
    report_path = os.path.join(save_folder, "error_report.txt")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"处理完成！结果已保存到 {save_folder} 目录")
    print(f"错误报告已保存到: {report_path}")

if __name__ == "__main__":
    main()



