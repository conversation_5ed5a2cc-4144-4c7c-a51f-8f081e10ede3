{"input_path": "test\\pp_structure_v3_demo.png", "page_index": null, "model_settings": {"use_doc_preprocessor": false, "use_seal_recognition": true, "use_table_recognition": true, "use_formula_recognition": true, "use_chart_recognition": false, "use_region_detection": true}, "parsing_res_list": [{"block_label": "doc_title", "block_content": "助力双方交往搭建友谊桥梁", "block_bbox": [133, 36, 1379, 123]}, {"block_label": "text", "block_content": "本报记者沈小晓任彦黄培昭", "block_bbox": [584, 159, 927, 179]}, {"block_label": "image", "block_content": "", "block_bbox": [774, 201, 1502, 685]}, {"block_label": "figure_title", "block_content": "在厄立特里亚不久前举办的第六届中国风筝文化节上，当地小学生体验风筝制作。\n中国驻厄立特里亚大使馆供图", "block_bbox": [808, 704, 1484, 747]}, {"block_label": "text", "block_content": "身着中国传统民族服装的厄立特里亚青年依次登台表演中国民族舞、现代舞、扇子舞等，曼妙的舞姿赢得现场观众阵阵掌声。这是日前危立特里亚高等教育与研究院孔子学院(以下简称“厄特孔院\")举办“喜迎新年\"中国歌舞比赛的场景。\n", "block_bbox": [9, 201, 358, 338]}, {"block_label": "text", "block_content": "中国和厄立特里亚传统友谊深厚。近年来，在高质量共建“一带一路”框架下，中厄两国人文交流不断深化，互利合作的民意基础日益深厚。\n", "block_bbox": [9, 345, 358, 435]}, {"block_label": "paragraph_title", "block_content": "“学好中文，我们的未来不是梦”\n", "block_bbox": [28, 456, 339, 514]}, {"block_label": "text", "block_content": "“鲜花曾告诉我你怎样走过，大地知道你心中的每一个角落……\"厄立特里亚阿斯马拉大学综合楼二层，一阵优美的歌声在走廊里回响。循着熟悉的旋律轻轻推开一间教室的门，学生们正跟着老师学唱中文歌曲《同一首歌》。", "block_bbox": [9, 536, 358, 651]}, {"block_label": "text", "block_content": "这是厄特孔院阿斯马拉大学教学点的一节中文歌曲课。为了让学生们更好地理解歌词大意，老师尤斯拉·穆罕默德萨尔·侯赛因逐字翻译和解释歌词。随着伴奏声响起，学生们边唱边随着节拍摇动身体，现场气氛热烈。", "block_bbox": [9, 658, 359, 770]}, {"block_label": "text", "block_content": "“这是中文歌曲初级班，共有32人。学生大部分来自首都阿斯马拉的中小学，年龄最小的仅有6岁。”尤斯拉告诉记者。", "block_bbox": [10, 776, 359, 842]}, {"block_label": "text", "block_content": "尤斯拉今年23岁，是厄立特里亚一所公立学校的艺术老师。她12岁开始在厄特孔院学习中文，在2017年第十届“汉语桥\"世界中学生中文比赛中获得厄立特里亚赛区第一名，并和同伴代表厄立特里亚前往中国参加决赛，获得团体优胜奖。2022年起，尤斯拉开始在厄特孔院兼职教授中文歌曲，每周末两个课时。“中国文化博大精深，我希望我的学生们能够通过中文歌曲更好地理解中国文化。”她说。", "block_bbox": [9, 848, 358, 1057]}, {"block_label": "text", "block_content": "“姐姐，你想去中国吗?”“非常想！我想去看故宫、爬长城。”尤斯拉的学生中有一对能歌善舞的姐妹，姐姐露娅今年15岁，妹妹莉娅14岁，两人都已在厄特孔院学习多年，中文说得格外流利。\n", "block_bbox": [9, 1064, 358, 1177]}, {"block_label": "text", "block_content": "露娅对记者说：“这些年来，怀着对中文和中国文化的热爱，我们姐妹俩始终相互鼓励，一起学习。我们的中文一天比一天好，还学会了中文歌和中国舞。我们一定要到中国去。学好中文，我们的未来不是梦！”", "block_bbox": [8, 1184, 358, 1297]}, {"block_label": "text", "block_content": "据厄特孔院中方院长黄鸣飞介绍，这所孔院成立于2013年3月，由贵州财经大学和", "block_bbox": [10, 1304, 358, 1346]}, {"block_label": "text", "block_content": "厄立特里亚高等教育与研究院合作建立，开设了中国语言课程和中国文化课程，注册学生2万余人次。10余年来，厄特孔院已成为当地民众了解中国的一扇窗口。", "block_bbox": [388, 200, 740, 290]}, {"block_label": "text", "block_content": "黄鸣飞表示，随着来学习中文的人日益增多，阿斯马拉大学教学点已难以满足教学需要。2024年4月，由中企蜀道集团所属四川路桥承建的孔院教学楼项目在阿斯马拉开工建设，预计今年上半年竣工，建成后将为危特孔院提供全新的办学场地。", "block_bbox": [389, 297, 740, 435]}, {"block_label": "paragraph_title", "block_content": "“在中国学习的经历让我看到更广阔的世界”", "block_bbox": [409, 456, 718, 515]}, {"block_label": "text", "block_content": "多年来，厄立特里亚广大赴华留学生和培训人员积极投身国家建设，成为助力该国发展的人才和厄中友好的见证者和推动者。", "block_bbox": [389, 537, 740, 603]}, {"block_label": "text", "block_content": "在厄立特里亚全国妇女联盟工作的约翰娜·特韦尔德·凯莱塔就是其中一位。她曾在中华女子学院攻读硕士学位，研究方向是女性领导力与社会发展。其间，她实地走访中国多个地区，获得了观察中国社会发展的第一手资料。\n", "block_bbox": [389, 609, 740, 745]}, {"block_label": "text", "block_content": "谈起在中国求学的经历，约翰娜记忆犹新：“中国的发展在当今世界是独一无二的。沿着中国特色社会主义道路坚定前行，中国创造了发展奇迹，这一切都离不开中国共产党的领导。中国的发展经验值得许多国家学习借鉴。”\n", "block_bbox": [389, 752, 740, 889]}, {"block_label": "text", "block_content": "正在西南大学学习的厄立特里亚博士生穆卢盖塔·泽穆伊对中国怀有深厚感情。8年前，在北京师范大学获得硕士学位后，穆卢盖塔在社交媒体上写下这样一段话：“这是我人生的重要一步，自此我拥有了一双坚固的鞋子，赋予我穿越荆棘的力量。”", "block_bbox": [389, 896, 740, 1033]}, {"block_label": "text", "block_content": "穆卢盖塔密切关注中国在经济、科技、教育等领域的发展，“中国在科研等方面的实力与日俱增。在中国学习的经历让我看到更广阔的世界，从中受益匪浅。”", "block_bbox": [389, 1040, 740, 1129]}, {"block_label": "text", "block_content": "23岁的莉迪亚·埃斯蒂法诺斯已在厄特孔院学习3年，在中国书法、中国画等方面表现干分优秀，在2024年厄立特里亚赛区的“汉语桥”比赛中获得一等奖。莉迪亚说：“学习中国书法让我的内心变得安宁和纯粹。我也喜欢中国的服饰，希望未来能去中国学习，把中国不同民族元素融入服装设计中，创作出更多精美作品，也把厄特文化分享给更多的中国朋友。”\n", "block_bbox": [389, 1136, 740, 1345]}, {"block_label": "text", "block_content": "“不管远近都是客人，请不用客气；相约好了在一起，我们欢迎你……”在一场中厄青年联谊活动上，四川路桥中方员工同当地大学生合唱《北京欢迎你》。厄立特里亚技术学院计算机科学与工程专业学生鲁夫塔·谢拉是其中一名演唱者，她很早便在孔院学习中文，一直在为去中国留学作准备。“这句歌词是我们两国人民友谊的生动写照。无论是投身于厄立特里亚基础设施建设的中企员工，还是在中国留学的厄立特里亚学子，两国人民携手努力，必将推动两国关系不断向前发展。”鲁夫塔说。\n", "block_bbox": [769, 776, 1121, 1058]}, {"block_label": "text", "block_content": "厄立特里亚高等教育委员会主任助理萨马瑞表示：“每年我们都会组织学生到中国访问学习，自前有超过5000名厄立特里亚学生在中国留学。学习中国的教育经验，有助于提升厄立特里亚的教育水平。”", "block_bbox": [770, 1064, 1121, 1177]}, {"block_label": "paragraph_title", "block_content": "“共同向世界展示非洲和亚洲的灿烂文明”", "block_bbox": [790, 1200, 1102, 1259]}, {"block_label": "text", "block_content": "从阿斯马拉出发，沿着蜿蜒曲折的盘山公路一路向东寻找丝路印迹。驱车两个小时，记者来到位于厄立特里亚港口城市马萨", "block_bbox": [770, 1280, 1122, 1346]}, {"block_label": "text", "block_content": "瓦的北红海省博物馆。", "block_bbox": [1154, 776, 1331, 794]}, {"block_label": "text", "block_content": "博物馆二层陈列着一个发掘自阿杜利斯古城的中国古代陶制酒器，罐身上写着“万”“和”“禅”“山”等汉字。“这件文物证明，很早以前我们就通过海上丝绸之路进行贸易往来与文化交流。这也是厄立特里亚与中国友好交往历史的有力证明。”北红海省博物馆研究与文献部负责人伊萨亚斯·特斯法兹吉说。\n", "block_bbox": [1152, 800, 1502, 986]}, {"block_label": "text", "block_content": "厄立特里亚国家博物馆考古学和人类学研究员菲尔蒙·特韦尔德十分喜爱中国文化。他表示：“学习彼此的语言和文化，将帮助厄中两国人民更好地理解彼此，助力双方交往，搭建友谊桥梁。”\n", "block_bbox": [1152, 992, 1502, 1106]}, {"block_label": "text", "block_content": "厄立特里亚国家博物馆馆长塔吉丁·努重达姆·优素福曾多次访问中国，对中华文明的传承与创新、现代化博物馆的建设与发展印象深刻。“中国博物馆不仅有许多保存完好的文物，还充分运用先进科技手段进行展示，帮助人们更好理解中华文明。”塔吉丁说，“危立特里亚与中国都拥有悠久的文明，始终相互理解、相互尊重。我希望未来与中国同行加强合作，共同向世界展示非洲和亚洲的灿烂文明。”\n", "block_bbox": [1151, 1112, 1502, 1346]}], "layout_det_res": {"input_path": null, "page_index": null, "boxes": [{"cls_id": 1, "label": "image", "score": 0.986474335193634, "coordinate": [774.82177734375, 201.053955078125, 1502.097412109375, 685.7725830078125]}, {"cls_id": 2, "label": "text", "score": 0.9859225749969482, "coordinate": [769.8651123046875, 776.2447509765625, 1121.5987548828125, 1058.4161376953125]}, {"cls_id": 2, "label": "text", "score": 0.9857137203216553, "coordinate": [1151.981201171875, 1112.5374755859375, 1502.784423828125, 1346.3548583984375]}, {"cls_id": 2, "label": "text", "score": 0.9847245216369629, "coordinate": [389.03271484375, 1136.3551025390625, 740.2325439453125, 1345.9288330078125]}, {"cls_id": 2, "label": "text", "score": 0.9842527508735657, "coordinate": [1152.150634765625, 800.1627197265625, 1502.12646484375, 986.1524658203125]}, {"cls_id": 2, "label": "text", "score": 0.9840854406356812, "coordinate": [9.159103393554688, 848.869873046875, 358.5721435546875, 1057.831298828125]}, {"cls_id": 2, "label": "text", "score": 0.9802595973014832, "coordinate": [9.336196899414062, 201.10110473632812, 358.3157958984375, 338.7887878417969]}, {"cls_id": 2, "label": "text", "score": 0.9801408648490906, "coordinate": [389.1559143066406, 297.41168212890625, 740.0758056640625, 435.41705322265625]}, {"cls_id": 2, "label": "text", "score": 0.9793584942817688, "coordinate": [389.18902587890625, 752.0958862304688, 740.0831909179688, 889.8800659179688]}, {"cls_id": 2, "label": "text", "score": 0.9793420433998108, "coordinate": [389.0252685546875, 896.3409423828125, 740.7423095703125, 1033.947021484375]}, {"cls_id": 2, "label": "text", "score": 0.9776490330696106, "coordinate": [8.950454711914062, 1184.783935546875, 358.75115966796875, 1297.876220703125]}, {"cls_id": 2, "label": "text", "score": 0.9773518443107605, "coordinate": [770.7178955078125, 1064.5711669921875, 1121.2249755859375, 1177.9925537109375]}, {"cls_id": 2, "label": "text", "score": 0.9773057103157043, "coordinate": [389.3818664550781, 609.707275390625, 740.055908203125, 745.32177734375]}, {"cls_id": 2, "label": "text", "score": 0.9765841960906982, "coordinate": [1152.0111083984375, 992.2964477539062, 1502.4925537109375, 1106.11669921875]}, {"cls_id": 2, "label": "text", "score": 0.9761469960212708, "coordinate": [9.465896606445312, 536.9925537109375, 358.20648193359375, 651.32080078125]}, {"cls_id": 2, "label": "text", "score": 0.9754040241241455, "coordinate": [9.35516357421875, 1064.3057861328125, 358.4532775878906, 1177.8336181640625]}, {"cls_id": 2, "label": "text", "score": 0.973055362701416, "coordinate": [9.932815551757812, 345.3626708984375, 358.034912109375, 435.1651611328125]}, {"cls_id": 2, "label": "text", "score": 0.9722563028335571, "coordinate": [388.91717529296875, 200.9364013671875, 740.0076293945312, 290.80712890625]}, {"cls_id": 2, "label": "text", "score": 0.9710620641708374, "coordinate": [389.39501953125, 1040.318359375, 740.009033203125, 1129.717041015625]}, {"cls_id": 2, "label": "text", "score": 0.969692051410675, "coordinate": [9.614028930664062, 658.1124267578125, 359.06170654296875, 770.0281982421875]}, {"cls_id": 2, "label": "text", "score": 0.9664220213890076, "coordinate": [770.2357177734375, 1280.4560546875, 1122.09326171875, 1346.47412109375]}, {"cls_id": 2, "label": "text", "score": 0.959757387638092, "coordinate": [389.66705322265625, 537.5613403320312, 740.0626831054688, 603.1770629882812]}, {"cls_id": 2, "label": "text", "score": 0.9594364166259766, "coordinate": [10.16400146484375, 776.8642578125, 359.0816955566406, 842.1767578125]}, {"cls_id": 2, "label": "text", "score": 0.9484673142433167, "coordinate": [10.403396606445312, 1304.7744140625, 358.9429931640625, 1346.374755859375]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.9476054310798645, "coordinate": [28.159500122070312, 456.76275634765625, 339.5616455078125, 514.9667358398438]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.9427523016929626, "coordinate": [790.69921875, 1200.3660888671875, 1102.38037109375, 1259.1649169921875]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.9424155950546265, "coordinate": [409.02789306640625, 456.6834716796875, 718.8147583007812, 515.5760498046875]}, {"cls_id": 10, "label": "doc_title", "score": 0.9376341104507446, "coordinate": [133.77923583984375, 36.884307861328125, 1379.668212890625, 123.46966552734375]}, {"cls_id": 2, "label": "text", "score": 0.9020199775695801, "coordinate": [584.9168701171875, 159.14181518554688, 927.22802734375, 179.01602172851562]}, {"cls_id": 2, "label": "text", "score": 0.8951595425605774, "coordinate": [1154.33642578125, 776.7466430664062, 1331.8544921875, 794.2299194335938]}, {"cls_id": 6, "label": "figure_title", "score": 0.789044976234436, "coordinate": [808.96533203125, 704.25537109375, 1484.060791015625, 747.2296142578125]}]}, "overall_ocr_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": true}, "dt_polys": [[[129, 42], [709, 40], [710, 138], [129, 140]], [[808, 38], [1389, 36], [1389, 138], [808, 140]], [[589, 163], [759, 163], [759, 184], [589, 184]], [[778, 165], [803, 165], [803, 181], [778, 181]], [[819, 165], [842, 165], [842, 182], [819, 182]], [[863, 163], [928, 163], [928, 184], [863, 184]], [[51, 206], [357, 206], [357, 223], [51, 223]], [[396, 206], [739, 206], [739, 223], [396, 223]], [[14, 230], [357, 230], [357, 247], [14, 247]], [[396, 230], [738, 230], [738, 247], [396, 247]], [[14, 254], [357, 254], [357, 270], [14, 270]], [[395, 253], [739, 253], [739, 272], [395, 272]], [[15, 279], [356, 279], [356, 293], [15, 293]], [[396, 278], [639, 278], [639, 295], [396, 295]], [[14, 302], [357, 302], [357, 319], [14, 319]], [[432, 302], [739, 302], [739, 319], [432, 319]], [[13, 324], [160, 324], [160, 344], [13, 344]], [[396, 325], [739, 325], [739, 342], [396, 342]], [[51, 350], [357, 350], [357, 367], [51, 367]], [[396, 350], [739, 350], [739, 367], [396, 367]], [[14, 374], [357, 374], [357, 391], [14, 391]], [[396, 374], [739, 374], [739, 391], [396, 391]], [[14, 398], [358, 398], [358, 415], [14, 415]], [[395, 397], [739, 398], [739, 416], [395, 415]], [[14, 422], [108, 422], [108, 439], [14, 439]], [[396, 422], [625, 422], [625, 438], [396, 438]], [[83, 460], [340, 460], [340, 487], [83, 487]], [[451, 458], [722, 460], [722, 487], [450, 485]], [[32, 494], [197, 494], [197, 521], [32, 521]], [[414, 496], [718, 496], [718, 520], [414, 520]], [[48, 542], [357, 541], [357, 558], [48, 559]], [[432, 542], [739, 542], [739, 559], [432, 559]], [[14, 565], [356, 565], [356, 582], [14, 582]], [[396, 565], [739, 565], [739, 582], [396, 582]], [[14, 590], [357, 590], [357, 606], [14, 606]], [[396, 590], [732, 590], [732, 606], [396, 606]], [[14, 613], [357, 613], [357, 630], [14, 630]], [[431, 613], [739, 613], [739, 630], [431, 630]], [[14, 637], [355, 637], [355, 654], [14, 654]], [[395, 637], [739, 637], [739, 654], [395, 654]], [[51, 661], [357, 661], [357, 678], [51, 678]], [[394, 659], [740, 660], [740, 680], [394, 679]], [[14, 684], [358, 684], [358, 701], [14, 701]], [[394, 683], [739, 684], [739, 702], [394, 701]], [[13, 708], [357, 707], [357, 725], [13, 726]], [[394, 707], [739, 708], [739, 726], [394, 725]], [[812, 707], [1449, 705], [1449, 725], [812, 727]], [[14, 733], [358, 733], [358, 750], [14, 750]], [[392, 729], [468, 731], [467, 752], [392, 749]], [[1249, 730], [1483, 729], [1483, 749], [1249, 750]], [[14, 757], [333, 757], [333, 773], [14, 773]], [[431, 757], [739, 757], [739, 773], [431, 773]], [[48, 780], [356, 780], [356, 797], [48, 797]], [[395, 780], [739, 780], [739, 797], [395, 797]], [[811, 780], [1120, 780], [1120, 797], [811, 797]], [[1156, 778], [1331, 778], [1331, 798], [1156, 798]], [[14, 804], [357, 804], [357, 821], [14, 821]], [[395, 804], [739, 804], [739, 821], [395, 821]], [[775, 803], [1121, 802], [1121, 822], [775, 823]], [[1193, 802], [1503, 801], [1503, 821], [1193, 822]], [[14, 828], [303, 828], [303, 845], [14, 845]], [[396, 828], [739, 828], [739, 845], [396, 845]], [[777, 829], [1121, 829], [1121, 846], [777, 846]], [[1156, 827], [1503, 825], [1503, 845], [1156, 847]], [[50, 851], [357, 852], [357, 870], [50, 869]], [[395, 852], [738, 852], [738, 869], [395, 869]], [[777, 853], [1121, 853], [1121, 870], [777, 870]], [[1157, 851], [1503, 851], [1503, 871], [1157, 871]], [[14, 877], [356, 877], [356, 894], [14, 894]], [[393, 874], [456, 874], [456, 896], [393, 896]], [[777, 877], [1121, 877], [1121, 894], [777, 894]], [[1157, 875], [1503, 874], [1503, 894], [1157, 895]], [[15, 901], [357, 901], [357, 918], [15, 918]], [[432, 901], [739, 901], [739, 918], [432, 918]], [[777, 901], [1120, 901], [1120, 918], [777, 918]], [[1158, 901], [1502, 901], [1502, 918], [1158, 918]], [[14, 925], [358, 925], [358, 941], [14, 941]], [[396, 925], [740, 925], [740, 941], [396, 941]], [[777, 925], [1121, 925], [1121, 941], [777, 941]], [[1159, 925], [1502, 925], [1502, 941], [1159, 941]], [[14, 948], [357, 948], [357, 965], [14, 965]], [[396, 948], [739, 948], [739, 965], [396, 965]], [[778, 948], [1120, 948], [1120, 965], [778, 965]], [[1158, 948], [1502, 948], [1502, 965], [1158, 965]], [[14, 972], [357, 972], [357, 989], [14, 989]], [[395, 972], [739, 972], [739, 989], [395, 989]], [[777, 972], [1120, 972], [1120, 989], [777, 989]], [[1156, 971], [1266, 971], [1266, 991], [1156, 991]], [[14, 996], [357, 996], [357, 1013], [14, 1013]], [[395, 996], [738, 996], [738, 1013], [395, 1013]], [[777, 996], [1120, 996], [1120, 1013], [777, 1013]], [[1195, 996], [1502, 996], [1502, 1013], [1195, 1013]], [[14, 1020], [357, 1020], [357, 1037], [14, 1037]], [[393, 1018], [647, 1019], [647, 1039], [393, 1038]], [[777, 1021], [1121, 1021], [1121, 1038], [777, 1038]], [[1158, 1021], [1502, 1021], [1502, 1038], [1158, 1038]], [[14, 1045], [295, 1045], [295, 1062], [14, 1062]], [[432, 1045], [739, 1045], [739, 1062], [432, 1062]], [[777, 1045], [905, 1045], [905, 1062], [777, 1062]], [[1159, 1045], [1502, 1045], [1502, 1062], [1159, 1062]], [[49, 1069], [358, 1069], [358, 1086], [49, 1086]], [[396, 1069], [739, 1069], [739, 1086], [396, 1086]], [[813, 1069], [1121, 1069], [1121, 1086], [813, 1086]], [[1159, 1069], [1502, 1069], [1502, 1086], [1159, 1086]], [[14, 1093], [358, 1093], [358, 1110], [14, 1110]], [[396, 1093], [738, 1093], [738, 1110], [396, 1110]], [[777, 1093], [1121, 1093], [1121, 1110], [777, 1110]], [[1159, 1093], [1340, 1093], [1340, 1110], [1159, 1110]], [[14, 1116], [358, 1116], [358, 1133], [14, 1133]], [[396, 1116], [606, 1116], [606, 1133], [396, 1133]], [[779, 1118], [1119, 1118], [1119, 1132], [779, 1132]], [[1195, 1116], [1502, 1116], [1502, 1133], [1195, 1133]], [[14, 1140], [358, 1140], [358, 1157], [14, 1157]], [[431, 1140], [739, 1140], [739, 1157], [431, 1157]], [[778, 1142], [1119, 1142], [1119, 1156], [778, 1156]], [[1160, 1142], [1501, 1142], [1501, 1156], [1160, 1156]], [[13, 1163], [178, 1163], [178, 1183], [13, 1183]], [[396, 1166], [738, 1166], [738, 1180], [396, 1180]], [[777, 1165], [1023, 1165], [1023, 1182], [777, 1182]], [[1160, 1166], [1501, 1166], [1501, 1180], [1160, 1180]], [[50, 1189], [358, 1189], [358, 1206], [50, 1206]], [[396, 1190], [738, 1190], [738, 1204], [396, 1204]], [[1160, 1190], [1501, 1190], [1501, 1204], [1160, 1204]], [[845, 1203], [1103, 1205], [1103, 1232], [845, 1230]], [[14, 1213], [358, 1213], [358, 1230], [14, 1230]], [[395, 1213], [739, 1213], [739, 1230], [395, 1230]], [[1159, 1213], [1502, 1213], [1502, 1230], [1159, 1230]], [[14, 1237], [358, 1237], [358, 1254], [14, 1254]], [[397, 1237], [739, 1237], [739, 1254], [397, 1254]], [[797, 1240], [1075, 1240], [1075, 1264], [797, 1264]], [[1159, 1237], [1502, 1237], [1502, 1254], [1159, 1254]], [[14, 1261], [358, 1261], [358, 1278], [14, 1278]], [[396, 1261], [738, 1261], [738, 1278], [396, 1278]], [[1159, 1261], [1502, 1261], [1502, 1278], [1159, 1278]], [[14, 1284], [297, 1284], [297, 1301], [14, 1301]], [[396, 1284], [739, 1284], [739, 1301], [396, 1301]], [[814, 1284], [1120, 1284], [1120, 1301], [814, 1301]], [[1159, 1284], [1502, 1284], [1502, 1301], [1159, 1301]], [[51, 1308], [358, 1308], [358, 1325], [51, 1325]], [[396, 1308], [738, 1308], [738, 1325], [396, 1325]], [[777, 1308], [1121, 1308], [1121, 1325], [777, 1325]], [[1158, 1308], [1502, 1308], [1502, 1325], [1158, 1325]], [[14, 1332], [358, 1332], [358, 1349], [14, 1349]], [[394, 1330], [506, 1330], [506, 1350], [394, 1350]], [[776, 1332], [1121, 1332], [1121, 1349], [776, 1349]], [[1156, 1330], [1234, 1330], [1234, 1351], [1156, 1351]]], "text_det_params": {"limit_side_len": 736, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["助力双方交往", "搭建友谊桥梁", "本报记者沈小晓", "任", "彦", "黄培昭", "身着中国传统民族服装的厄立特里亚青", "厄立特里亚高等教育与研究院合作建立，开", "年依次登台表演中国民族舞、现代舞、扇子舞", "设了中国语言课程和中国文化课程，注册学", "等，曼妙的舞姿赢得现场观众阵阵掌声。这", "生2万余人次。10余年来，厄特孔院已成为", "是日前危立特里亚高等教育与研究院孔子学", "当地民众了解中国的一扇窗口。", "院(以下简称“厄特孔院\")举办“喜迎新年\"中国", "黄鸣飞表示，随着来学习中文的人日益", "歌舞比赛的场景。", "增多，阿斯马拉大学教学点已难以满足教学", "中国和厄立特里亚传统友谊深厚。近年", "需要。2024年4月，由中企蜀道集团所属四", "来，在高质量共建“一带一路”框架下，中厄两", "川路桥承建的孔院教学楼项目在阿斯马拉开", "国人文交流不断深化，互利合作的民意基础", "工建设，预计今年上半年竣工，建成后将为危", "日益深厚。", "特孔院提供全新的办学场地。", "“学好中文，我们的", "“在中国学习的经历", "未来不是梦”", "让我看到更广阔的世界”", "“鲜花曾告诉我你怎样走过，大地知道你", "多年来，厄立特里亚广大赴华留学生和", "心中的每一个角落……\"厄立特里亚阿斯马拉", "培训人员积极投身国家建设，成为助力该国", "大学综合楼二层，一阵优美的歌声在走廊里回", "发展的人才和厄中友好的见证者和推动者。", "响。循着熟悉的旋律轻轻推开一间教室的门，", "在厄立特里亚全国妇女联盟工作的约翰", "学生们正跟着老师学唱中文歌曲《同一首歌》。", "娜·特韦尔德·凯莱塔就是其中一位。她曾在", "这是厄特孔院阿斯马拉大学教学点的一", "中华女子学院攻读硕士学位，研究方向是女", "节中文歌曲课。为了让学生们更好地理解歌", "性领导力与社会发展。其间，她实地走访中国", "词大意，老师尤斯拉·穆罕默德萨尔·侯赛因逐", "多个地区，获得了观察中国社会发展的第一", "在厄立特里亚不久前举办的第六届中国风筝文化节上，当地小学生体验风筝制作。", "字翻译和解释歌词。随着伴奏声响起，学生们", "手资料。", "中国驻厄立特里亚大使馆供图", "边唱边随着节拍摇动身体，现场气氛热烈。", "谈起在中国求学的经历，约翰娜记忆犹", "“这是中文歌曲初级班，共有32人。学", "新：“中国的发展在当今世界是独一无二的。", "“不管远近都是客人，请不用客气；相约", "瓦的北红海省博物馆。", "生大部分来自首都阿斯马拉的中小学，年龄", "沿着中国特色社会主义道路坚定前行，中国", "好了在一起，我们欢迎你……”在一场中厄青", "博物馆二层陈列着一个发掘自阿杜利", "最小的仅有6岁。”尤斯拉告诉记者。", "创造了发展奇迹，这一切都离不开中国共产党", "年联谊活动上，四川路桥中方员工同当地大", "斯古城的中国古代陶制酒器，罐身上写着", "尤斯拉今年23岁，是厄立特里亚一所公立", "的领导。中国的发展经验值得许多国家学习", "学生合唱《北京欢迎你》。厄立特里亚技术学", "“万”“和”“禅”“山”等汉字。“这件文物证", "学校的艺术老师。她12岁开始在厄特孔院学", "借鉴。”", "院计算机科学与工程专业学生鲁夫塔·谢拉", "明，很早以前我们就通过海上丝绸之路进行", "习中文，在2017年第十届“汉语桥\"世界中学生", "正在西南大学学习的厄立特里亚博士生", "是其中一名演唱者，她很早便在孔院学习中", "贸易往来与文化交流。这也是厄立特里亚", "中文比赛中获得厄立特里亚赛区第一名，并和", "穆卢盖塔·泽穆伊对中国怀有深厚感情。8", "文，一直在为去中国留学作准备。“这句歌词", "与中国友好交往历史的有力证明。”北红海", "同伴代表厄立特里亚前往中国参加决赛，获得", "年前，在北京师范大学获得硕士学位后，穆卢", "是我们两国人民友谊的生动写照。无论是投", "省博物馆研究与文献部负责人伊萨亚斯·特", "团体优胜奖。2022年起，尤斯拉开始在厄特孔", "盖塔在社交媒体上写下这样一段话：“这是我", "身于厄立特里亚基础设施建设的中企员工，", "斯法兹吉说。", "院兼职教授中文歌曲，每周末两个课时。“中国", "人生的重要一步，自此我拥有了一双坚固的", "还是在中国留学的厄立特里亚学子，两国人", "厄立特里亚国家博物馆考古学和人类学", "文化博大精深，我希望我的学生们能够通过中", "鞋子，赋予我穿越荆棘的力量。”", "民携手努力，必将推动两国关系不断向前发", "研究员菲尔蒙·特韦尔德十分喜爱中国文", "文歌曲更好地理解中国文化。”她说。", "穆卢盖塔密切关注中国在经济、科技、教", "展。”鲁夫塔说。", "化。他表示：“学习彼此的语言和文化，将帮", "“姐姐，你想去中国吗?”“非常想！我想", "育等领域的发展，“中国在科研等方面的实力", "厄立特里亚高等教育委员会主任助理萨", "助厄中两国人民更好地理解彼此，助力双方", "去看故宫、爬长城。”尤斯拉的学生中有一对", "与日俱增。在中国学习的经历让我看到更广", "马瑞表示：“每年我们都会组织学生到中国访", "交往，搭建友谊桥梁。”", "能歌善舞的姐妹，姐姐露娅今年15岁，妹妹", "阔的世界，从中受益匪浅。”", "问学习，自前有超过5000名厄立特里亚学生", "厄立特里亚国家博物馆馆长塔吉丁·努", "莉娅14岁，两人都已在厄特孔院学习多年，", "23岁的莉迪亚·埃斯蒂法诺斯已在厄特", "在中国留学。学习中国的教育经验，有助于", "重达姆·优素福曾多次访问中国，对中华文明", "中文说得格外流利。", "孔院学习3年，在中国书法、中国画等方面表", "提升厄立特里亚的教育水平。”", "的传承与创新、现代化博物馆的建设与发展", "露娅对记者说：“这些年来，怀着对中文", "现干分优秀，在2024年厄立特里亚赛区的", "印象深刻。“中国博物馆不仅有许多保存完好", "“共同向世界展示非", "和中国文化的热爱，我们姐妹俩始终相互鼓", "“汉语桥”比赛中获得一等奖。莉迪亚说：“学", "的文物，还充分运用先进科技手段进行展示，", "励，一起学习。我们的中文一天比一天好，还", "习中国书法让我的内心变得安宁和纯粹。我", "洲和亚洲的灿烂文明”", "帮助人们更好理解中华文明。”塔吉丁说，“危", "学会了中文歌和中国舞。我们一定要到中国", "也喜欢中国的服饰，希望未来能去中国学习，", "立特里亚与中国都拥有悠久的文明，始终相", "去。学好中文，我们的未来不是梦！”", "把中国不同民族元素融入服装设计中，创作", "从阿斯马拉出发，沿着蜿蜒曲折的盘山", "互理解、相互尊重。我希望未来与中国同行", "据厄特孔院中方院长黄鸣飞介绍，这所", "出更多精美作品，也把厄特文化分享给更多", "公路一路向东寻找丝路印迹。驱车两个小", "加强合作，共同向世界展示非洲和亚洲的灿", "孔院成立于2013年3月，由贵州财经大学和", "的中国朋友。”", "时，记者来到位于厄立特里亚港口城市马萨", "烂文明。”"], "rec_scores": [0.9911889433860779, 0.9954431056976318, 0.9997568726539612, 0.9998762607574463, 0.9976855516433716, 0.9990552067756653, 0.9991164207458496, 0.9851100444793701, 0.9969385266304016, 0.9995278716087341, 0.9750460386276245, 0.9953802227973938, 0.9740821719169617, 0.9995507001876831, 0.9081490635871887, 0.9987804293632507, 0.9993906021118164, 0.9929639101028442, 0.9936696290969849, 0.9988605976104736, 0.967623233795166, 0.9990717172622681, 0.9928038716316223, 0.9621984362602234, 0.9998204112052917, 0.9998384714126587, 0.9792872667312622, 0.993209183216095, 0.9990474581718445, 0.9660419821739197, 0.990016520023346, 0.9976691007614136, 0.9539782404899597, 0.9978024363517761, 0.9980373382568359, 0.9893016815185547, 0.9995555877685547, 0.9997082948684692, 0.999658465385437, 0.9964515566825867, 0.9997997283935547, 0.9944863319396973, 0.9996855854988098, 0.9983586072921753, 0.9946371912956238, 0.9983947277069092, 0.9976778030395508, 0.991075336933136, 0.9997761249542236, 0.9971624612808228, 0.9989851117134094, 0.9970260858535767, 0.9843085408210754, 0.9934447407722473, 0.9853003025054932, 0.9997439384460449, 0.9899899363517761, 0.9981187582015991, 0.954047679901123, 0.9991429448127747, 0.9831859469413757, 0.9983455538749695, 0.9979070425033569, 0.9963356852531433, 0.9765005707740784, 0.9997932314872742, 0.9744914770126343, 0.9727046489715576, 0.9979356527328491, 0.9820445775985718, 0.9958842396736145, 0.9961361885070801, 0.9657728672027588, 0.9998621940612793, 0.9926291108131409, 0.982044517993927, 0.9940502047538757, 0.9968761801719666, 0.9970816373825073, 0.978786051273346, 0.99467533826828, 0.9969879388809204, 0.9995947480201721, 0.999101459980011, 0.9954372048377991, 0.9867194890975952, 0.9997563362121582, 0.9997082352638245, 0.996025800704956, 0.9974357485771179, 0.9984593391418457, 0.9993398189544678, 0.9956817626953125, 0.9846107959747314, 0.9986860156059265, 0.997313380241394, 0.971892774105072, 0.9986290335655212, 0.9504942297935486, 0.9903730154037476, 0.9554158449172974, 0.9994204640388489, 0.9985269904136658, 0.9887903928756714, 0.9804815053939819, 0.9912127256393433, 0.9895722270011902, 0.9869236350059509, 0.9929596185684204, 0.9817359447479248, 0.9630118012428284, 0.9943785071372986, 0.9768196940422058, 0.9995110630989075, 0.9976119995117188, 0.991936206817627, 0.9998342990875244, 0.9970216751098633, 0.9891351461410522, 0.993037760257721, 0.9873946309089661, 0.9428390264511108, 0.9961743354797363, 0.9948196411132812, 0.9972955584526062, 0.9713424444198608, 0.9973337054252625, 0.9959301948547363, 0.9996628761291504, 0.996279239654541, 0.9667094945907593, 0.9996217489242554, 0.9977564811706543, 0.9978173971176147, 0.9822071194648743, 0.9978743195533752, 0.9776185154914856, 0.9991853833198547, 0.9979324340820312, 0.9987461566925049, 0.9996656775474548, 0.9977776408195496, 0.9987490773200989, 0.9386845827102661, 0.9967305064201355, 0.9511805772781372], "rec_polys": [[[129, 42], [709, 40], [710, 138], [129, 140]], [[808, 38], [1389, 36], [1389, 138], [808, 140]], [[589, 163], [759, 163], [759, 184], [589, 184]], [[778, 165], [803, 165], [803, 181], [778, 181]], [[819, 165], [842, 165], [842, 182], [819, 182]], [[863, 163], [928, 163], [928, 184], [863, 184]], [[51, 206], [357, 206], [357, 223], [51, 223]], [[396, 206], [739, 206], [739, 223], [396, 223]], [[14, 230], [357, 230], [357, 247], [14, 247]], [[396, 230], [738, 230], [738, 247], [396, 247]], [[14, 254], [357, 254], [357, 270], [14, 270]], [[395, 253], [739, 253], [739, 272], [395, 272]], [[15, 279], [356, 279], [356, 293], [15, 293]], [[396, 278], [639, 278], [639, 295], [396, 295]], [[14, 302], [357, 302], [357, 319], [14, 319]], [[432, 302], [739, 302], [739, 319], [432, 319]], [[13, 324], [160, 324], [160, 344], [13, 344]], [[396, 325], [739, 325], [739, 342], [396, 342]], [[51, 350], [357, 350], [357, 367], [51, 367]], [[396, 350], [739, 350], [739, 367], [396, 367]], [[14, 374], [357, 374], [357, 391], [14, 391]], [[396, 374], [739, 374], [739, 391], [396, 391]], [[14, 398], [358, 398], [358, 415], [14, 415]], [[395, 397], [739, 398], [739, 416], [395, 415]], [[14, 422], [108, 422], [108, 439], [14, 439]], [[396, 422], [625, 422], [625, 438], [396, 438]], [[83, 460], [340, 460], [340, 487], [83, 487]], [[451, 458], [722, 460], [722, 487], [450, 485]], [[32, 494], [197, 494], [197, 521], [32, 521]], [[414, 496], [718, 496], [718, 520], [414, 520]], [[48, 542], [357, 541], [357, 558], [48, 559]], [[432, 542], [739, 542], [739, 559], [432, 559]], [[14, 565], [356, 565], [356, 582], [14, 582]], [[396, 565], [739, 565], [739, 582], [396, 582]], [[14, 590], [357, 590], [357, 606], [14, 606]], [[396, 590], [732, 590], [732, 606], [396, 606]], [[14, 613], [357, 613], [357, 630], [14, 630]], [[431, 613], [739, 613], [739, 630], [431, 630]], [[14, 637], [355, 637], [355, 654], [14, 654]], [[395, 637], [739, 637], [739, 654], [395, 654]], [[51, 661], [357, 661], [357, 678], [51, 678]], [[394, 659], [740, 660], [740, 680], [394, 679]], [[14, 684], [358, 684], [358, 701], [14, 701]], [[394, 683], [739, 684], [739, 702], [394, 701]], [[13, 708], [357, 707], [357, 725], [13, 726]], [[394, 707], [739, 708], [739, 726], [394, 725]], [[812, 707], [1449, 705], [1449, 725], [812, 727]], [[14, 733], [358, 733], [358, 750], [14, 750]], [[392, 729], [468, 731], [467, 752], [392, 749]], [[1249, 730], [1483, 729], [1483, 749], [1249, 750]], [[14, 757], [333, 757], [333, 773], [14, 773]], [[431, 757], [739, 757], [739, 773], [431, 773]], [[48, 780], [356, 780], [356, 797], [48, 797]], [[395, 780], [739, 780], [739, 797], [395, 797]], [[811, 780], [1120, 780], [1120, 797], [811, 797]], [[1156, 778], [1331, 778], [1331, 798], [1156, 798]], [[14, 804], [357, 804], [357, 821], [14, 821]], [[395, 804], [739, 804], [739, 821], [395, 821]], [[775, 803], [1121, 802], [1121, 822], [775, 823]], [[1193, 802], [1503, 801], [1503, 821], [1193, 822]], [[14, 828], [303, 828], [303, 845], [14, 845]], [[396, 828], [739, 828], [739, 845], [396, 845]], [[777, 829], [1121, 829], [1121, 846], [777, 846]], [[1156, 827], [1503, 825], [1503, 845], [1156, 847]], [[50, 851], [357, 852], [357, 870], [50, 869]], [[395, 852], [738, 852], [738, 869], [395, 869]], [[777, 853], [1121, 853], [1121, 870], [777, 870]], [[1157, 851], [1503, 851], [1503, 871], [1157, 871]], [[14, 877], [356, 877], [356, 894], [14, 894]], [[393, 874], [456, 874], [456, 896], [393, 896]], [[777, 877], [1121, 877], [1121, 894], [777, 894]], [[1157, 875], [1503, 874], [1503, 894], [1157, 895]], [[15, 901], [357, 901], [357, 918], [15, 918]], [[432, 901], [739, 901], [739, 918], [432, 918]], [[777, 901], [1120, 901], [1120, 918], [777, 918]], [[1158, 901], [1502, 901], [1502, 918], [1158, 918]], [[14, 925], [358, 925], [358, 941], [14, 941]], [[396, 925], [740, 925], [740, 941], [396, 941]], [[777, 925], [1121, 925], [1121, 941], [777, 941]], [[1159, 925], [1502, 925], [1502, 941], [1159, 941]], [[14, 948], [357, 948], [357, 965], [14, 965]], [[396, 948], [739, 948], [739, 965], [396, 965]], [[778, 948], [1120, 948], [1120, 965], [778, 965]], [[1158, 948], [1502, 948], [1502, 965], [1158, 965]], [[14, 972], [357, 972], [357, 989], [14, 989]], [[395, 972], [739, 972], [739, 989], [395, 989]], [[777, 972], [1120, 972], [1120, 989], [777, 989]], [[1156, 971], [1266, 971], [1266, 991], [1156, 991]], [[14, 996], [357, 996], [357, 1013], [14, 1013]], [[395, 996], [738, 996], [738, 1013], [395, 1013]], [[777, 996], [1120, 996], [1120, 1013], [777, 1013]], [[1195, 996], [1502, 996], [1502, 1013], [1195, 1013]], [[14, 1020], [357, 1020], [357, 1037], [14, 1037]], [[393, 1018], [647, 1019], [647, 1039], [393, 1038]], [[777, 1021], [1121, 1021], [1121, 1038], [777, 1038]], [[1158, 1021], [1502, 1021], [1502, 1038], [1158, 1038]], [[14, 1045], [295, 1045], [295, 1062], [14, 1062]], [[432, 1045], [739, 1045], [739, 1062], [432, 1062]], [[777, 1045], [905, 1045], [905, 1062], [777, 1062]], [[1159, 1045], [1502, 1045], [1502, 1062], [1159, 1062]], [[49, 1069], [358, 1069], [358, 1086], [49, 1086]], [[396, 1069], [739, 1069], [739, 1086], [396, 1086]], [[813, 1069], [1121, 1069], [1121, 1086], [813, 1086]], [[1159, 1069], [1502, 1069], [1502, 1086], [1159, 1086]], [[14, 1093], [358, 1093], [358, 1110], [14, 1110]], [[396, 1093], [738, 1093], [738, 1110], [396, 1110]], [[777, 1093], [1121, 1093], [1121, 1110], [777, 1110]], [[1159, 1093], [1340, 1093], [1340, 1110], [1159, 1110]], [[14, 1116], [358, 1116], [358, 1133], [14, 1133]], [[396, 1116], [606, 1116], [606, 1133], [396, 1133]], [[779, 1118], [1119, 1118], [1119, 1132], [779, 1132]], [[1195, 1116], [1502, 1116], [1502, 1133], [1195, 1133]], [[14, 1140], [358, 1140], [358, 1157], [14, 1157]], [[431, 1140], [739, 1140], [739, 1157], [431, 1157]], [[778, 1142], [1119, 1142], [1119, 1156], [778, 1156]], [[1160, 1142], [1501, 1142], [1501, 1156], [1160, 1156]], [[13, 1163], [178, 1163], [178, 1183], [13, 1183]], [[396, 1166], [738, 1166], [738, 1180], [396, 1180]], [[777, 1165], [1023, 1165], [1023, 1182], [777, 1182]], [[1160, 1166], [1501, 1166], [1501, 1180], [1160, 1180]], [[50, 1189], [358, 1189], [358, 1206], [50, 1206]], [[396, 1190], [738, 1190], [738, 1204], [396, 1204]], [[1160, 1190], [1501, 1190], [1501, 1204], [1160, 1204]], [[845, 1203], [1103, 1205], [1103, 1232], [845, 1230]], [[14, 1213], [358, 1213], [358, 1230], [14, 1230]], [[395, 1213], [739, 1213], [739, 1230], [395, 1230]], [[1159, 1213], [1502, 1213], [1502, 1230], [1159, 1230]], [[14, 1237], [358, 1237], [358, 1254], [14, 1254]], [[397, 1237], [739, 1237], [739, 1254], [397, 1254]], [[797, 1240], [1075, 1240], [1075, 1264], [797, 1264]], [[1159, 1237], [1502, 1237], [1502, 1254], [1159, 1254]], [[14, 1261], [358, 1261], [358, 1278], [14, 1278]], [[396, 1261], [738, 1261], [738, 1278], [396, 1278]], [[1159, 1261], [1502, 1261], [1502, 1278], [1159, 1278]], [[14, 1284], [297, 1284], [297, 1301], [14, 1301]], [[396, 1284], [739, 1284], [739, 1301], [396, 1301]], [[814, 1284], [1120, 1284], [1120, 1301], [814, 1301]], [[1159, 1284], [1502, 1284], [1502, 1301], [1159, 1301]], [[51, 1308], [358, 1308], [358, 1325], [51, 1325]], [[396, 1308], [738, 1308], [738, 1325], [396, 1325]], [[777, 1308], [1121, 1308], [1121, 1325], [777, 1325]], [[1158, 1308], [1502, 1308], [1502, 1325], [1158, 1325]], [[14, 1332], [358, 1332], [358, 1349], [14, 1349]], [[394, 1330], [506, 1330], [506, 1350], [394, 1350]], [[776, 1332], [1121, 1332], [1121, 1349], [776, 1349]], [[1156, 1330], [1234, 1330], [1234, 1351], [1156, 1351]]], "rec_boxes": [[129, 40, 710, 140], [808, 36, 1389, 140], [589, 163, 759, 184], [778, 165, 803, 181], [819, 165, 842, 182], [863, 163, 928, 184], [51, 206, 357, 223], [396, 206, 739, 223], [14, 230, 357, 247], [396, 230, 738, 247], [14, 254, 357, 270], [395, 253, 739, 272], [15, 279, 356, 293], [396, 278, 639, 295], [14, 302, 357, 319], [432, 302, 739, 319], [13, 324, 160, 344], [396, 325, 739, 342], [51, 350, 357, 367], [396, 350, 739, 367], [14, 374, 357, 391], [396, 374, 739, 391], [14, 398, 358, 415], [395, 397, 739, 416], [14, 422, 108, 439], [396, 422, 625, 438], [83, 460, 340, 487], [450, 458, 722, 487], [32, 494, 197, 521], [414, 496, 718, 520], [48, 541, 357, 559], [432, 542, 739, 559], [14, 565, 356, 582], [396, 565, 739, 582], [14, 590, 357, 606], [396, 590, 732, 606], [14, 613, 357, 630], [431, 613, 739, 630], [14, 637, 355, 654], [395, 637, 739, 654], [51, 661, 357, 678], [394, 659, 740, 680], [14, 684, 358, 701], [394, 683, 739, 702], [13, 707, 357, 726], [394, 707, 739, 726], [812, 705, 1449, 727], [14, 733, 358, 750], [392, 729, 468, 752], [1249, 729, 1483, 750], [14, 757, 333, 773], [431, 757, 739, 773], [48, 780, 356, 797], [395, 780, 739, 797], [811, 780, 1120, 797], [1156, 778, 1331, 798], [14, 804, 357, 821], [395, 804, 739, 821], [775, 802, 1121, 823], [1193, 801, 1503, 822], [14, 828, 303, 845], [396, 828, 739, 845], [777, 829, 1121, 846], [1156, 825, 1503, 847], [50, 851, 357, 870], [395, 852, 738, 869], [777, 853, 1121, 870], [1157, 851, 1503, 871], [14, 877, 356, 894], [393, 874, 456, 896], [777, 877, 1121, 894], [1157, 874, 1503, 895], [15, 901, 357, 918], [432, 901, 739, 918], [777, 901, 1120, 918], [1158, 901, 1502, 918], [14, 925, 358, 941], [396, 925, 740, 941], [777, 925, 1121, 941], [1159, 925, 1502, 941], [14, 948, 357, 965], [396, 948, 739, 965], [778, 948, 1120, 965], [1158, 948, 1502, 965], [14, 972, 357, 989], [395, 972, 739, 989], [777, 972, 1120, 989], [1156, 971, 1266, 991], [14, 996, 357, 1013], [395, 996, 738, 1013], [777, 996, 1120, 1013], [1195, 996, 1502, 1013], [14, 1020, 357, 1037], [393, 1018, 647, 1039], [777, 1021, 1121, 1038], [1158, 1021, 1502, 1038], [14, 1045, 295, 1062], [432, 1045, 739, 1062], [777, 1045, 905, 1062], [1159, 1045, 1502, 1062], [49, 1069, 358, 1086], [396, 1069, 739, 1086], [813, 1069, 1121, 1086], [1159, 1069, 1502, 1086], [14, 1093, 358, 1110], [396, 1093, 738, 1110], [777, 1093, 1121, 1110], [1159, 1093, 1340, 1110], [14, 1116, 358, 1133], [396, 1116, 606, 1133], [779, 1118, 1119, 1132], [1195, 1116, 1502, 1133], [14, 1140, 358, 1157], [431, 1140, 739, 1157], [778, 1142, 1119, 1156], [1160, 1142, 1501, 1156], [13, 1163, 178, 1183], [396, 1166, 738, 1180], [777, 1165, 1023, 1182], [1160, 1166, 1501, 1180], [50, 1189, 358, 1206], [396, 1190, 738, 1204], [1160, 1190, 1501, 1204], [845, 1203, 1103, 1232], [14, 1213, 358, 1230], [395, 1213, 739, 1230], [1159, 1213, 1502, 1230], [14, 1237, 358, 1254], [397, 1237, 739, 1254], [797, 1240, 1075, 1264], [1159, 1237, 1502, 1254], [14, 1261, 358, 1278], [396, 1261, 738, 1278], [1159, 1261, 1502, 1278], [14, 1284, 297, 1301], [396, 1284, 739, 1301], [814, 1284, 1120, 1301], [1159, 1284, 1502, 1301], [51, 1308, 358, 1325], [396, 1308, 738, 1325], [777, 1308, 1121, 1325], [1158, 1308, 1502, 1325], [14, 1332, 358, 1349], [394, 1330, 506, 1350], [776, 1332, 1121, 1349], [1156, 1330, 1234, 1351]]}}