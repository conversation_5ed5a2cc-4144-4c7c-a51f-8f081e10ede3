Global:
  model_type: det
  model_dir: ./models/ch_PP-OCRv4_det_server_infer
  model_filename: inference.pdmodel
  params_filename: inference.pdiparams
  algorithm: DB
  
Distillation:
  alpha: 1.0
  loss: l2

QuantAware:
  use_pact: false
  activation_bits: 8
  is_full_quantize: false
  onnx_format: false
  activation_quantize_type: moving_average_abs_max
  weight_quantize_type: channel_wise_abs_max
  not_quant_pattern:
  - skip_quant
  quantize_op_types:
  - conv2d
  weight_bits: 8

TrainConfig:
  epochs: 1
  eval_iter: 200
  learning_rate: 
    type: CosineAnnealingDecay 
    learning_rate: 0.000005
  optimizer_builder:
    optimizer:
      type: Adam
    weight_decay: 5.0e-05

PostProcess:
  name: DBPostProcess
  thresh: 0.3
  box_thresh: 0.6
  max_candidates: 1000
  unclip_ratio: 1.5
  
Metric:
  name: DetMetric
  main_indicator: hmean
  
Train:
  dataset:
    name: SimpleDataSet
    data_dir: datasets/chinese
    label_file_list:
        - datasets/chinese/zhongce_training_fix_1.6k.txt
        - datasets/chinese/label_train_all_f4_part2.txt
        - datasets/chinese/label_train_all_f4_part3.txt
        - datasets/chinese/label_train_all_f4_part4.txt
        - datasets/chinese/label_train_all_f4_part5.txt
        - datasets/chinese/synth_en_my_clip.txt
        - datasets/chinese/synth_ch_my_clip.txt
        - datasets/chinese/synth_en_my_largeword_clip.txt
    ratio_list:
        - 0.3
        - 0.2
        - 0.1
        - 0.2
        - 0.2
        - 0.1
        - 0.2
        - 0.2
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - DetLabelEncode: null
    - IaaAugment:
        augmenter_args:
        - type: Fliplr
          args:
            p: 0.5
        - type: Affine
          args:
            rotate:
            - -10
            - 10
        - type: Resize
          args:
            size:
            - 0.5
            - 3
    - EastRandomCropData:
        size:
        - 960
        - 960
        max_tries: 50
        keep_ratio: true
    - MakeBorderMap:
        shrink_ratio: 0.4
        thresh_min: 0.3
        thresh_max: 0.7
    - MakeShrinkMap:
        shrink_ratio: 0.4
        min_text_size: 8
    - NormalizeImage:
        scale: 1./255.
        mean:
        - 0.485
        - 0.456
        - 0.406
        std:
        - 0.229
        - 0.224
        - 0.225
        order: hwc
    - ToCHWImage: null
    - KeepKeys:
        keep_keys:
        - image
        - threshold_map
        - threshold_mask
        - shrink_map
        - shrink_mask
  loader:
    shuffle: true
    drop_last: false
    batch_size_per_card: 2
    num_workers: 8
    
Eval:
  dataset:
    name: SimpleDataSet
    data_dir: datasets/v4_4_test_dataset_small
    label_file_list:
      - datasets/v4_4_test_dataset_small/label.txt
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - DetLabelEncode: null
    - DetResizeForTest: null
    - NormalizeImage:
        scale: 1./255.
        mean:
        - 0.485
        - 0.456
        - 0.406
        std:
        - 0.229
        - 0.224
        - 0.225
        order: hwc
    - ToCHWImage: null
    - KeepKeys:
        keep_keys:
        - image
        - shape
        - polys
        - ignore_tags
  loader:
    shuffle: false
    drop_last: false
    batch_size_per_card: 1
    num_workers: 2
