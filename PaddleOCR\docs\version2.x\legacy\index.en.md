---
typora-copy-images-to: images
comments: true
hide:
  - toc
---

# Legacy Features

## Overview

This section introduces the features and models related to the PaddleOCR 2.x branch. Due to the upgrades in the 3.x branch, some models and features are no longer compatible with the older branch. Therefore, users who need to use or refer to the features of the older branch can refer to this part of the documentation.

## Models Supported by PaddleOCR 2.x Branch:

* [Model List](model_list.md)

## Features Supported by PaddleOCR 2.x Branch:

* [Inference with Python Prediction Engine](python_infer.en.md)
* [Inference with C++ Prediction Engine](cpp_infer.en.md)
* [Compilation Guide for Visual Studio 2019 Community CMake](windows_vs2019_build.en.md)
* [Service-Oriented Deployment](paddle_server.en.md)
* [Android Deployment](android_demo.en.md)
* [Jetson Deployment](Jetson_infer.en.md)
* [Edge Device Deployment](lite.en.md)
* [Web Frontend Deployment](paddle_js.en.md)
* [Paddle2ONNX Model Conversion and Prediction](paddle2onnx.en.md)
* [PaddlePaddle Cloud Deployment Tool](paddle_cloud.en.md)
* [Benchmark](benchmark.en.md)
