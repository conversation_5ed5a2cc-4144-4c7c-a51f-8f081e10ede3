import java.security.MessageDigest

apply plugin: 'com.android.application'

android {
    compileSdkVersion 29
    defaultConfig {
        applicationId "com.baidu.paddle.lite.demo.ocr"
        minSdkVersion 23
        targetSdkVersion 29
        versionCode 2
        versionName "2.0"
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        externalNativeBuild {
            cmake {
                cppFlags "-std=c++11 -frtti -fexceptions -Wno-format"
                arguments '-DANDROID_PLATFORM=android-23', '-DANDROID_STL=c++_shared' ,"-DANDROID_ARM_NEON=TRUE"
            }
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.10.2"
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
}

def archives = [
        [
                'src' : 'https://paddleocr.bj.bcebos.com/libs/paddle_lite_libs_v2_10.tar.gz',
                'dest': 'PaddleLite'
        ],
        [
                'src' : 'https://paddlelite-demo.bj.bcebos.com/libs/android/opencv-4.2.0-android-sdk.tar.gz',
                'dest': 'OpenCV'
        ],
        [
                'src' : 'https://paddleocr.bj.bcebos.com/PP-OCRv2/lite/ch_PP-OCRv2.tar.gz',
                'dest' : 'src/main/assets/models'
        ],
        [
                'src' : 'https://paddleocr.bj.bcebos.com/dygraph_v2.0/lite/ch_dict.tar.gz',
                'dest' : 'src/main/assets/labels'
        ]
]

task downloadAndExtractArchives(type: DefaultTask) {
    doFirst {
        println "Downloading and extracting archives including libs and models"
    }
    doLast {
        // Prepare cache folder for archives
        String cachePath = "cache"
        if (!file("${cachePath}").exists()) {
            mkdir "${cachePath}"
        }
        archives.eachWithIndex { archive, index ->
            MessageDigest messageDigest = MessageDigest.getInstance('MD5')
            messageDigest.update(archive.src.bytes)
            String cacheName = new BigInteger(1, messageDigest.digest()).toString(32)
            // Download the target archive if not exists
            boolean copyFiles = !file("${archive.dest}").exists()
            if (!file("${cachePath}/${cacheName}.tar.gz").exists()) {
                ant.get(src: archive.src, dest: file("${cachePath}/${cacheName}.tar.gz"))
                copyFiles = true; // force to copy files from the latest archive files
            }
            // Extract the target archive if its dest path does not exists
            if (copyFiles) {
                copy {
                    from tarTree("${cachePath}/${cacheName}.tar.gz")
                    into "${archive.dest}"
                }
            }
        }
    }
}
preBuild.dependsOn downloadAndExtractArchives
