{"input_path": "test_images\\image.png", "page_index": null, "model_settings": {"use_doc_preprocessor": false, "use_seal_recognition": true, "use_table_recognition": true, "use_formula_recognition": true, "use_chart_recognition": false, "use_region_detection": true}, "parsing_res_list": [{"block_label": "header", "block_content": "人", "block_bbox": [531, 23, 704, 42]}, {"block_label": "header", "block_content": "1958年6月8日星期日", "block_bbox": [32, 33, 208, 45]}, {"block_label": "doc_title", "block_content": "各国兄弟党代表继续在保共七大致词 一致声讨南共现代修正主义 代表大会通过各项决议并选举了中央机构", "block_bbox": [219, 66, 716, 183]}, {"block_label": "text", "block_content": "新华社索亚6日电保加利亚共产党代表大会6日下午结束了对部长会议主席于哥夫报告的词论：发言者一致回意这个报告。", "block_bbox": [28, 59, 170, 103]}, {"block_label": "text", "block_content": "在6日上午会议上，保共中央政治局委员拉·达米扬诺夫发了言，他着重指出，保加利亚共产党的特征是它在思想上对于敌对阶级的不可调和性以及对伟大的马克思列宁主义思想的无限忠诚，也还谈到保加利亚动力工业的成就和速一步迅速发展动力工业的任务以及隆低采媒和电力生产成本的间题。\n", "block_bbox": [28, 106, 170, 220]}, {"block_label": "text", "block_content": "在下午会议上，保共中央政治局委员塔斯科夫就南斯拉夫商业组织的特点，批判了南共的修正主义。他说：南斯拉夫领导人站在关于国家在这个阶段“消亡”的这种反马克思主义的修正主义立场上：给予生产企业为官们自己的产品寻找国内市场和国外市场的权利，同时它们完全有自由在自己之间进行列的竞争，自发地改变市场价格。毫无疑问，这种在自发势力影响下经常改变价格的情况首先就影响到劳动人民的切身利益，并具可以促成意争、破产和香并财产的情况。\n", "block_bbox": [28, 224, 169, 397]}, {"block_label": "text", "block_content": "在6日会议上，有许多国家的共产党和工人党的代表向保共代表", "block_bbox": [29, 399, 171, 419]}, {"block_label": "text", "block_content": "大会致祝词。", "block_bbox": [177, 200, 226, 209]}, {"block_label": "text", "block_content": "蒙古人民革命党中央委员会政治局委员达姆工说，聘们高光地看到，蒙古人民革命党、保加利亚共产党、苏联共产党以及其他兄弟共产党和工人党对南斯拉夫共产主义者联盟领导人的观点作出了一致的评价，这些观点的目的是反对马思克列宁主义基本原则，反对社会主隆营，国限共产主义调动和工运动的团结和期固。\n", "block_bbox": [177, 212, 318, 327]}, {"block_label": "text", "block_content": "达姆丁说，我们同你们在一起坚决反击现代修正主义否认苏联具有世界历更意义的经验的调言。", "block_bbox": [177, 329, 318, 362]}, {"block_label": "text", "block_content": "利亚和黎巴嫩共产党款书记哈立德·巴格达什说，现在，世界舆论应当看清楚南斯拉夫修正主义者在阿拉伯问题上所进行的破坏活动，见尔格莱德的修正主义集团的", "block_bbox": [177, 365, 319, 421]}, {"block_label": "text", "block_content": "使者在阿拉伯国家中不仅竭力使阿拉伯共产主义运动脱离开整个共产主义运动和使其分化，而且还使民性解放运动脱离开反对童国主义，主要是反对美帝国主义的斗争的路线，他们千方百计地破坏阿拉伯国家和苏联之间的友谊，千方百计地", "block_bbox": [325, 199, 466, 279]}, {"block_label": "text", "block_content": "分裂民族解放运动和碳坏反帝爱国阵线。他们就是这样在阿拉伯世界“发展”马克思列宁主义的，但是，他们的破坏调动必将失败。", "block_bbox": [474, 199, 614, 243]}, {"block_label": "text", "block_content": "章大利共产党中央委员会书记保罗·布法利尼说，我们不赞成南斯拉夫同志所采取的不正确的立", "block_bbox": [473, 246, 613, 279]}, {"block_label": "paragraph_title", "block_content": "赫鲁晓夫代表苏联最高苏维埃", "block_bbox": [358, 292, 567, 306]}, {"block_label": "paragraph_title", "block_content": "授予达米扬诺夫列宁勋章", "block_bbox": [338, 319, 605, 340]}, {"block_label": "text", "block_content": "新华社索非亚6日电荷鲁晓夫6日下午在保共领导人日夫科夫加理夫等陪同下，拜访了正在病中的保加利亚国民议会主席团主席格达米扬诺夫。\n", "block_bbox": [325, 351, 609, 384]}, {"block_label": "text", "block_content": "在拜会时，赫鲁晓夫把苏联最高苏维埃主席团在达米扬诺夫六十五岁寿晨时授予他的列宁助章交给了他，祝贺达米扬诺夫六十五岁寿辰和他在加强保苏友谊方面的贡献。\n", "block_bbox": [325, 388, 612, 421]}, {"block_label": "text", "block_content": "场，并且给予了必要的、明确的和坚决的批评，我们认为，在阶级分析和国际形势的分析方面，他们是不正确的，是不符合事实的，特别是在苏联的和平倡议得到有效地和有希望地发展的时候，能们所果期的立场对和平事业就更加有害。", "block_bbox": [621, 198, 762, 278]}, {"block_label": "text", "block_content": "阿根廷共产党中央委员会政治局委员塔迪奥利说，阿根廷共产党赞同苏联和其他国家共产党对南共钢领草案和南斯拉夫共产主义者联盟第七次代表大会上有关这个纲领的发言的批评，阿根廷共产党始终忠于和平宣言和它给社会主义国家共产党和工人党宣育的支持。\n", "block_bbox": [620, 281, 763, 384]}, {"block_label": "text", "block_content": "这一天，代表大会宣读了土耳其共产党的贺词。贺词说，土耳其共产党强烈地反对南斯拉夫共产主", "block_bbox": [621, 387, 763, 419]}, {"block_label": "text", "block_content": "义者联盟针对苏联、其他社会丰义国家和世界共产主义运动和工人运动而发的排诱论调，这些修正丰义者企图向土其其人民表明，美国惠魔是能们的“朋友”，然而他们的企图是不会成功的，他们是要枉费心机的。他们离间不了各国共产党和工人党。\n", "block_bbox": [767, 55, 911, 147]}, {"block_label": "text", "block_content": "6日下午向保共代表大会致祝词的还有英国共产党、摩洛哥共产党、芬兰共产党、奥地利共产党、丹妻共产党、伊朗人民党、伊拉克共产党、苏丹共产党以及其他共产党和工人党的代表团团长。", "block_bbox": [768, 151, 910, 218]}, {"block_label": "text", "block_content": "新华社索非亚7日电保加利亚共产党第七次代表大会今天上午一致通过了关于保加利亚共产党中央委员会的总结报告的决议，代表们完全赞同第六次代表大会以来常中央的政治路线与活动，会设还一政通过了关于中央检查委员会的总结报告的决议，关于保共七大对发展国民经济的第三个五年计划指示的决议。大会的代表并且对党章作了一些修改和补充。\n", "block_bbox": [769, 222, 910, 348]}, {"block_label": "text", "block_content": "利亚在上午的会议上还选举了保加", "block_bbox": [769, 352, 909, 371]}, {"block_label": "text", "block_content": "祝词的有瑞典，挪盛、以色列、瑞士、卢森堡和加章大等国兄弟党的代表。\n", "block_bbox": [769, 375, 910, 419]}, {"block_label": "number", "block_content": ".3", "block_bbox": [1153, 30, 1195, 39]}, {"block_label": "doc_title", "block_content": "杜克洛对伦敦“工人日报”记者说", "block_bbox": [77, 448, 415, 470]}, {"block_label": "doc_title", "block_content": "法国人民能够制止个人独裁", "block_bbox": [62, 487, 436, 516]}, {"block_label": "text", "block_content": "里昂举行反法西斯群众大会", "block_bbox": [85, 527, 406, 545]}, {"block_label": "text", "block_content": "新华杜伦款7日电法国共产党中央爱员会书记杜克洛在接见伦敦“工人日报”记者时说。“在法国存在着足以堵塞法西斯道路和制止行使个人权力的社会和政治力量。*\n", "block_bbox": [25, 563, 167, 631]}, {"block_label": "text", "block_content": "今天“工人日”登载的这个访问记援引杜克洛的语说，戴高乐政府的组成仅仅具有合法的外数。它实际上是国尔及尔和国耶佐军事政变的产物，同时也是弗林姆兰政府的可耻的投降和社会党总书记摩勒叛卖的结果，摩勒已经变成了戴高乐个人权力的工具。", "block_bbox": [25, 634, 167, 725]}, {"block_label": "text", "block_content": "国人民来说，戴高乐政府的成立章味着保卫共和国的斗争的新阶段的开始。\n", "block_bbox": [25, 729, 167, 772]}, {"block_label": "text", "block_content": "他说，戴高乐的部长们都是他个人的仆从，周围有这些仆从的戴高乐，对于共和国制度的威胁更大。因为他还拥有修改宪法的权力，能将拟定旨在给予他个人权力以宪法形式的害法草案。\n", "block_bbox": [25, 776, 166, 843]}, {"block_label": "text", "block_content": "杜克洛指出，载高乐分子想利用所谓救国委员会来搞一个法西斯性质的运动。这种运动在攻击政治", "block_bbox": [25, 847, 167, 886]}, {"block_label": "text", "block_content": "（所谓救国）委员会，是按照量索里尼的法西斯办法建立的。", "block_bbox": [176, 564, 316, 584]}, {"block_label": "text", "block_content": "正如戴高乐在阿尔及尔的演说刚刚表明的那样，新政府显示了它的本色，它是要求在阿尔及利亚规续进行战争的最反动的和最富有殖民主义性质的大资本转力的产物。而在阿尔及利亚继续进行战争是违反法国真正的利益的，并且带有把战争扩大到整个北非的严重危验。", "block_bbox": [174, 588, 316, 679]}, {"block_label": "text", "block_content": "杜克洛强调指出，法国共产党充分认识到自己所离负的责任，它已经及时地使得工人阶级和全国人民警惕起来，从直创造了让人民动员起来保卫共和国的条件，法国共产党的努力已经使得法西斯阴谋无法像他们原来计划的那样实现，现在法国共产常向全国发出的号召是：在工厂，城市和农村成立成干上万的共和国保卫委员会：加紧进行争取阿尔及利亚的和平、争取满足劳动人民的要求和争取缓和国际紧张局势的斗争。\n", "block_bbox": [174, 682, 316, 831]}, {"block_label": "text", "block_content": "新华社7日讯据塔斯林巴警7日讯：里昂举行了有三千人参加的反法西斯群众大会。", "block_bbox": [174, 835, 316, 867]}, {"block_label": "text", "block_content": "法国的第一座法西斯城市”。他号召里昂的共和人士提高警惕，在所有的企业中成立反法西斯委员会。", "block_bbox": [324, 564, 465, 597]}, {"block_label": "text", "block_content": "国民议会议员皮埃尔·戈特和加米耶·瓦兰也在大会上讲了话。", "block_bbox": [325, 599, 465, 619]}, {"block_label": "text", "block_content": "掘报纸报道，有三十五个政治团体和工会组织代表参加的蒙拜利埃市反法西斯委员会通过了一项决议。决议说，委员会“将维护它的团结，并将给予任何侵犯民主自由的阴谋以坚决反击”。", "block_bbox": [324, 623, 465, 690]}, {"block_label": "text", "block_content": "（新华杜）", "block_bbox": [710, 683, 747, 692]}, {"block_label": "doc_title", "block_content": "阿尔及利亚共产党书记布哈利 指斥戴高乐极端殖民主义政策 民族解放阵线拒绝在阿尔及利亚选举", "block_bbox": [341, 707, 747, 803]}, {"block_label": "text", "block_content": "据新华杜7日讯据捷克斯洛伐克通讯社开罗7日消息：陶尔及利亚共产党书记拉比·布哈利就戴高乐4日在阿尔及尔发表的演讲，发表了谈话。\n", "block_bbox": [323, 823, 464, 879]}, {"block_label": "text", "block_content": "怎样回答他6月4日的宣言。我们的人民将进一步加强他们的解放斗争，因为他们知道，他们可以依靠突尼斯和摩洛哥兄弟人民的坚决的支持和全世界为自由、民主与和平而", "block_bbox": [472, 824, 614, 886]}, {"block_label": "text", "block_content": "阿巴斯6日到达开罗出席阿尔及利亚民族解放阵线联络与执行委员会会议，他对新闻记者说：“民族解放阵线不会接受法国强加于阿尔及利亚的任何帝国主义的解决办法，", "block_bbox": [620, 824, 761, 885]}, {"block_label": "paragraph_title", "block_content": "印度报纸刊载毛主席在十三陵水库劳动的照片", "block_bbox": [771, 448, 909, 483]}, {"block_label": "image", "block_content": "", "block_bbox": [472, 449, 763, 652]}, {"block_label": "text", "block_content": "当法国国民议会6月1日讨论授权戴高乐组织政府问题的时候，巴黎的劳动人民不题军警阻挠举行了反对戴高乐上台的示威游行。", "block_bbox": [472, 661, 762, 681]}, {"block_label": "text", "block_content": "新华杜新德里7日电印度教徒报”6日在第一版上刊登了毛泽东主席和中国共产党中央委员会的委员们在十三陵参加修建水座的劳动的照片，德里其他大报也刊登了这张照片。\n", "block_bbox": [768, 498, 910, 567]}, {"block_label": "text", "block_content": "孟买“自由新闻”在刊登这张照片时，还刊载了毛泽东主席在“红旗”杂志第一期上发表的文章的摘要。\n", "block_bbox": [768, 570, 910, 614]}, {"block_label": "text", "block_content": "“德里时代周刊”发表了评论赞扬中国领导人参加体力劳动的精神。\n", "block_bbox": [768, 618, 908, 650]}, {"block_label": "paragraph_title", "block_content": "对我国改革农具印度舆论很感兴趣", "block_bbox": [784, 665, 896, 701]}, {"block_label": "text", "block_content": "据新华社新德里7日电印度报纸登载的关于在北京举行的农具改革展览会的消息引起这里很大的兴趣。\n", "block_bbox": [767, 716, 909, 760]}, {"block_label": "text", "block_content": "“印度时报”最近发表杜论说：印度“必须学习制造成本低、节省劳动力、数力比较大的新农具。这正是中国在做的工作”。", "block_bbox": [767, 764, 909, 808]}, {"block_label": "text", "block_content": "在印度，目前最普通的耕地农具是用装有锐利的钢口刀的木型。", "block_bbox": [769, 811, 909, 831]}, {"block_label": "doc_title", "block_content": "黎人民军在贝鲁特痛击政府军", "block_bbox": [784, 847, 1188, 876]}, {"block_label": "doc_title", "block_content": "国际民主妇联第四届代表大会的成就 高高擎起保卫和平的旗帜", "block_bbox": [922, 88, 982, 475]}, {"block_label": "image", "block_content": "社論", "block_bbox": [989, 56, 1065, 88]}, {"block_label": "text", "block_content": "国际民主妇女联合会在维也纳举行的第四届代表大会，经过五天的会议以后，已经胜利闭", "block_bbox": [1073, 55, 1207, 88]}, {"block_label": "text", "block_content": "幕，参加这属大会的有来自七十六个国家的代表，们就“保卫生命”、“妇女在社会上执行母亲、劳动者和公民职责的条件”、“以和平和各国人民友好的精神来教育和培养儿童和青年的权利”等三个议题分别进行了讨论，并且代表全世界妇女和母亲们的共同愿望，打电报给案全理事会主席，呼吁设法制止核武器试验，通过这届大会，全世界妇女再一次显示了她们团结一致为维护和平，保卫妇女权利和儿童南福而斗争的力量。\n", "block_bbox": [990, 91, 1207, 193]}, {"block_label": "text", "block_content": "自从1953年在哥本哈根召开世界妇女大会以来，世界局势已经发生了深刻的变化，并且达到了一个新的转折点，以美国为首的帝国主义阵营，正在遣受新的严重的经济危机的冲击，而爱好和平的社会主义备国的情况却在蒸蒸日上，亚洲、非洲和拉丁美洲的民族独立退动继续高漆，法国、意大利、英国、日本等请本主义国家人民维护和平，争取民主的退动也在更加广泛地开展，苏联单方面停止核武器试险的决定、苏联和东欧备人民民丰国家的和平倡议、中国人民患愿军的从朝鲜撤退，受到了全世界爱好和平人民的热烈欢迎，有力地促进了世界和平运动的进展，苏联三个人造卫星的上天，标表着和平民主力量肯定地超过人民在争期和平、民主和民族独立的斗争中取得新的胜利。\n", "block_bbox": [990, 197, 1207, 383]}, {"block_label": "text", "block_content": "但是，以美国为首的帝国主义集团却顺固地企图阳挡历史潮流的前进，美帝国主义还在继续进行战争威胁和准备新的战争，因此，一切爱好和平的人们仍须保持高度的警惕，继续不懈地为维护和平而斗争。在这种情况下，国际民主妇女联合会第四属代表大会以绿亲名义向世界发出的和平呼吁，无疑地是有着后大的感召力量和深远的意义的。\n", "block_bbox": [990, 386, 1207, 466]}, {"block_label": "text", "block_content": "在保卫和平、反对战争的斗争中，妇女是一支伸大的力量，从国际民主妇女联合会总书记桑蒂的报告", "block_bbox": [991, 469, 1207, 490]}, {"block_label": "text", "block_content": "中，我们高兴地看到：最近又有许多国家的妇女组织加入了国际民主妇女联合会，而这些国家的妇女以前是没有组织起来和没有参加过社会生酒的，这不仅标志着某些国家妇女的进一步觉醒和世界妇女的进一步团结，直具也反映了世界和平力量的壮大和加强，国际民丰妇女联合会根捐各国妇女的共同愿望，在发挥妇女对保卫和平的积极作用方面，进行了许条有载的工作，我们相信，经过这次大会，在新形转的推动下，全世界的妇女将会更加加强团结和友谊，对世界和平作出更大的贡献。", "block_bbox": [917, 493, 1207, 573]}, {"block_label": "text", "block_content": "“没有争取和平和民族独立的有效斗争，就不可能争取到妇女和儿童的权利和幸福”，这句话说得很对，中国妇女从切身的经验中深深体会到这一点的正确，中国妇女珍视自己已经跟得的权利，正在同全国人民一道，鼓足干劲，为建设解放了的祖国而进行努力，因此特别感到和积，为使母亲和孩子能等永远生调在没有恐惧的世界里而共同奋斗，现在目前的条件下，就有可能制止战争狂人的冒险，为实现妇女的权利和儿童的幸福创造出首要的条件全世界的持久和平。", "block_bbox": [916, 576, 1207, 679]}, {"block_label": "doc_title", "block_content": "匈社会主义工人党举行中央全会", "block_bbox": [939, 707, 1190, 724]}, {"block_label": "text", "block_content": "新华杜布达佩斯7日电匈牙利杜会主义工人党中央委员会昨天举行了全体会议。\n", "block_bbox": [917, 738, 1206, 758]}, {"block_label": "text", "block_content": "中央委员会第一书记卡达尔在会议上作了关于经济互助委员会成员国共产党和工人党代表会议、华沙条约缝约国政治协商委员会会议以及匈苏两国经济谈判的报告，常中央委员会一政通过了这个报告，", "block_bbox": [916, 762, 1206, 795]}, {"block_label": "text", "block_content": "在会课上党中央政治局委员基仕·卡罗伊还报告了发展自牙利国民经济三年计划（1958—1960）的指示，中央委员会讨论了并且一致通过了这个指示。\n", "block_bbox": [917, 798, 1207, 830]}], "layout_det_res": {"input_path": null, "page_index": null, "boxes": [{"cls_id": 2, "label": "text", "score": 0.9843373894691467, "coordinate": [990.6006469726562, 197.42381286621094, 1207.35888671875, 383.0843505859375]}, {"cls_id": 1, "label": "image", "score": 0.9841985702514648, "coordinate": [472.49639892578125, 449.1783447265625, 763.2965698242188, 652.8564453125]}, {"cls_id": 2, "label": "text", "score": 0.9840864539146423, "coordinate": [28.376068115234375, 224.50897216796875, 169.99386596679688, 397.23101806640625]}, {"cls_id": 2, "label": "text", "score": 0.9827369451522827, "coordinate": [916.496826171875, 576.60009765625, 1207.531982421875, 679.32421875]}, {"cls_id": 2, "label": "text", "score": 0.9826353788375854, "coordinate": [28.12865447998047, 106.74650573730469, 170.76312255859375, 220.61659240722656]}, {"cls_id": 2, "label": "text", "score": 0.9821651577949524, "coordinate": [174.80001831054688, 682.5149536132812, 316.6431579589844, 831.6409301757812]}, {"cls_id": 2, "label": "text", "score": 0.9814451336860657, "coordinate": [25.396560668945312, 634.5927734375, 167.1466827392578, 725.678955078125]}, {"cls_id": 2, "label": "text", "score": 0.9812980890274048, "coordinate": [620.4818115234375, 281.3597412109375, 763.1461181640625, 384.6947021484375]}, {"cls_id": 2, "label": "text", "score": 0.9805338382720947, "coordinate": [990.5108642578125, 386.3390808105469, 1207.2713623046875, 466.2971496582031]}, {"cls_id": 2, "label": "text", "score": 0.9795657396316528, "coordinate": [917.3566284179688, 493.1573791503906, 1207.624755859375, 573.2828369140625]}, {"cls_id": 2, "label": "text", "score": 0.9786862730979919, "coordinate": [990.3955078125, 91.7490005493164, 1207.5078125, 193.82778930664062]}, {"cls_id": 2, "label": "text", "score": 0.9786858558654785, "coordinate": [25.21233367919922, 563.658447265625, 167.48876953125, 631.180419921875]}, {"cls_id": 2, "label": "text", "score": 0.978596568107605, "coordinate": [25.45030975341797, 776.3941650390625, 166.9398193359375, 843.608154296875]}, {"cls_id": 2, "label": "text", "score": 0.9783073663711548, "coordinate": [177.727783203125, 212.25753784179688, 318.5707702636719, 327.0019226074219]}, {"cls_id": 2, "label": "text", "score": 0.9782715439796448, "coordinate": [769.1512451171875, 222.15870666503906, 910.4832763671875, 348.980712890625]}, {"cls_id": 2, "label": "text", "score": 0.9775276780128479, "coordinate": [621.2205810546875, 198.1495361328125, 762.7066650390625, 278.0382080078125]}, {"cls_id": 2, "label": "text", "score": 0.9772199988365173, "coordinate": [174.690673828125, 588.351806640625, 316.509765625, 679.080322265625]}, {"cls_id": 2, "label": "text", "score": 0.9761319756507874, "coordinate": [767.53271484375, 55.97789764404297, 911.0721435546875, 147.55792236328125]}, {"cls_id": 2, "label": "text", "score": 0.9733866453170776, "coordinate": [768.2660522460938, 151.40267944335938, 910.1900024414062, 218.88015747070312]}, {"cls_id": 2, "label": "text", "score": 0.9720788598060608, "coordinate": [28.25627899169922, 59.81774139404297, 170.45535278320312, 103.81749725341797]}, {"cls_id": 2, "label": "text", "score": 0.9714230298995972, "coordinate": [767.7385864257812, 716.2313232421875, 909.6992797851562, 760.9957275390625]}, {"cls_id": 2, "label": "text", "score": 0.9712990522384644, "coordinate": [177.3751220703125, 365.5046081542969, 319.1434326171875, 421.1553649902344]}, {"cls_id": 2, "label": "text", "score": 0.9708470702171326, "coordinate": [768.9463500976562, 498.61383056640625, 910.4362182617188, 567.1624145507812]}, {"cls_id": 2, "label": "text", "score": 0.9700025320053101, "coordinate": [25.095252990722656, 729.4921875, 167.15292358398438, 772.591064453125]}, {"cls_id": 2, "label": "text", "score": 0.9696115851402283, "coordinate": [324.41973876953125, 623.5018310546875, 465.89447021484375, 690.9288330078125]}, {"cls_id": 2, "label": "text", "score": 0.9694765210151672, "coordinate": [917.0493774414062, 798.51416015625, 1207.091796875, 830.96142578125]}, {"cls_id": 2, "label": "text", "score": 0.9694622159004211, "coordinate": [916.715087890625, 762.35595703125, 1206.7529296875, 795.048583984375]}, {"cls_id": 2, "label": "text", "score": 0.9690322875976562, "coordinate": [767.2095947265625, 764.313720703125, 909.6783447265625, 808.598388671875]}, {"cls_id": 2, "label": "text", "score": 0.9688803553581238, "coordinate": [325.6791076660156, 199.2352294921875, 466.7190246582031, 279.31671142578125]}, {"cls_id": 2, "label": "text", "score": 0.9679421782493591, "coordinate": [768.3228759765625, 570.6009521484375, 910.12744140625, 614.6043701171875]}, {"cls_id": 2, "label": "text", "score": 0.9666829705238342, "coordinate": [323.84527587890625, 823.3118896484375, 464.05633544921875, 879.2205810546875]}, {"cls_id": 2, "label": "text", "score": 0.9655367136001587, "coordinate": [325.61346435546875, 351.995361328125, 609.1958618164062, 384.6539306640625]}, {"cls_id": 2, "label": "text", "score": 0.964305579662323, "coordinate": [325.33251953125, 388.24127197265625, 612.7122802734375, 421.00543212890625]}, {"cls_id": 2, "label": "text", "score": 0.9630914330482483, "coordinate": [474.7122802734375, 199.14749145507812, 614.05712890625, 243.13272094726562]}, {"cls_id": 2, "label": "text", "score": 0.9604817032814026, "coordinate": [324.94818115234375, 564.1542358398438, 465.14398193359375, 597.0355834960938]}, {"cls_id": 2, "label": "text", "score": 0.9585477709770203, "coordinate": [621.323974609375, 387.6330871582031, 763.419677734375, 419.8645324707031]}, {"cls_id": 2, "label": "text", "score": 0.9584617614746094, "coordinate": [174.86517333984375, 835.3477783203125, 316.1387939453125, 867.7974853515625]}, {"cls_id": 2, "label": "text", "score": 0.9573631286621094, "coordinate": [768.66552734375, 618.4605102539062, 908.3828125, 650.5333862304688]}, {"cls_id": 2, "label": "text", "score": 0.9553672671318054, "coordinate": [769.16748046875, 375.59063720703125, 910.328857421875, 419.51715087890625]}, {"cls_id": 2, "label": "text", "score": 0.9547860622406006, "coordinate": [177.73782348632812, 329.5823974609375, 318.4128112792969, 362.0303955078125]}, {"cls_id": 2, "label": "text", "score": 0.9535068273544312, "coordinate": [473.510498046875, 246.64483642578125, 613.935546875, 279.20782470703125]}, {"cls_id": 2, "label": "text", "score": 0.9531646966934204, "coordinate": [917.2784423828125, 738.4498291015625, 1206.7083740234375, 758.6944580078125]}, {"cls_id": 2, "label": "text", "score": 0.9429911375045776, "coordinate": [176.98724365234375, 564.1263427734375, 316.01788330078125, 584.9515380859375]}, {"cls_id": 2, "label": "text", "score": 0.9378759264945984, "coordinate": [769.3312377929688, 811.60400390625, 909.0580444335938, 831.72509765625]}, {"cls_id": 2, "label": "text", "score": 0.9340980648994446, "coordinate": [991.0042724609375, 469.6528015136719, 1207.2303466796875, 490.0668029785156]}, {"cls_id": 2, "label": "text", "score": 0.9328149557113647, "coordinate": [769.4297485351562, 352.58868408203125, 909.8967895507812, 371.95562744140625]}, {"cls_id": 12, "label": "header", "score": 0.9328047037124634, "coordinate": [531.0140991210938, 23.21684455871582, 704.3038940429688, 42.30680847167969]}, {"cls_id": 2, "label": "text", "score": 0.931601881980896, "coordinate": [29.634933471679688, 399.81768798828125, 171.39816284179688, 419.94281005859375]}, {"cls_id": 2, "label": "text", "score": 0.9270663261413574, "coordinate": [620.9815063476562, 824.167236328125, 761.6661987304688, 885.7681884765625]}, {"cls_id": 2, "label": "text", "score": 0.9171468019485474, "coordinate": [472.1319885253906, 824.2222900390625, 614.0093994140625, 886]}, {"cls_id": 12, "label": "header", "score": 0.915816605091095, "coordinate": [32.35359191894531, 33.87733459472656, 208.42922973632812, 45.93016052246094]}, {"cls_id": 2, "label": "text", "score": 0.9136214256286621, "coordinate": [325.01953125, 599.6224365234375, 465.43975830078125, 619.8748779296875]}, {"cls_id": 10, "label": "doc_title", "score": 0.8931958675384521, "coordinate": [784.951416015625, 847.625732421875, 1188.9149169921875, 876.142822265625]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.8919413685798645, "coordinate": [784.7172241210938, 665.5663452148438, 896.4212036132812, 701.2039184570312]}, {"cls_id": 1, "label": "image", "score": 0.8767729997634888, "coordinate": [989.9974975585938, 56.06816864013672, 1065.90869140625, 88.66436004638672]}, {"cls_id": 10, "label": "doc_title", "score": 0.8544326424598694, "coordinate": [922.3438720703125, 88.74545288085938, 982.91015625, 475.8450622558594]}, {"cls_id": 2, "label": "text", "score": 0.7811102867126465, "coordinate": [177.67044067382812, 200.71786499023438, 226.31442260742188, 209.19940185546875]}, {"cls_id": 10, "label": "doc_title", "score": 0.7594663500785828, "coordinate": [939.64111328125, 707.1842651367188, 1190.9462890625, 724.6359252929688]}, {"cls_id": 2, "label": "text", "score": 0.7534446716308594, "coordinate": [1073.870849609375, 55.732025146484375, 1207.476318359375, 88.21685791015625]}, {"cls_id": 10, "label": "doc_title", "score": 0.7407506704330444, "coordinate": [341.6822814941406, 707.567138671875, 747.2222900390625, 803.8092041015625]}, {"cls_id": 10, "label": "doc_title", "score": 0.7280300259590149, "coordinate": [219.96273803710938, 66.31629943847656, 716.2904052734375, 183.04872131347656]}, {"cls_id": 3, "label": "number", "score": 0.7085291147232056, "coordinate": [1153.15380859375, 30.955997467041016, 1195.94482421875, 39.87283706665039]}, {"cls_id": 2, "label": "text", "score": 0.6646993160247803, "coordinate": [25.85547637939453, 847.5618286132812, 167.2510986328125, 886]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.6568062901496887, "coordinate": [771.4658203125, 448.73797607421875, 909.78857421875, 483.61700439453125]}, {"cls_id": 2, "label": "text", "score": 0.6428311467170715, "coordinate": [472.0613098144531, 661.1646728515625, 762.9716796875, 681.4793701171875]}, {"cls_id": 2, "label": "text", "score": 0.6390912532806396, "coordinate": [710.26123046875, 683.9569702148438, 747.120849609375, 692.5429077148438]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.6008431315422058, "coordinate": [338.57012939453125, 319.0888977050781, 605.3929443359375, 340.8282775878906]}, {"cls_id": 10, "label": "doc_title", "score": 0.5655919313430786, "coordinate": [62.02992248535156, 487.6131591796875, 436.67388916015625, 516.428955078125]}, {"cls_id": 10, "label": "doc_title", "score": 0.5565620064735413, "coordinate": [77.59205627441406, 448.4481201171875, 415.7491455078125, 470.055908203125]}, {"cls_id": 0, "label": "paragraph_title", "score": 0.5278307795524597, "coordinate": [358.43292236328125, 292.0457458496094, 567.0830688476562, 306.6246032714844]}, {"cls_id": 10, "label": "doc_title", "score": 0.5197917222976685, "coordinate": [276.6417236328125, 66.58950805664062, 661.5236206054688, 94.35400390625]}, {"cls_id": 10, "label": "doc_title", "score": 0.517687201499939, "coordinate": [387.07684326171875, 707.908203125, 701.7031860351562, 730.11669921875]}, {"cls_id": 2, "label": "text", "score": 0.4493864178657532, "coordinate": [85.62886047363281, 527.2383422851562, 406.6922607421875, 545.4171752929688]}]}, "overall_ocr_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": true}, "dt_polys": [[[531, 22], [562, 22], [562, 45], [531, 45]], [[31, 32], [141, 32], [141, 48], [31, 48]], [[159, 32], [211, 32], [211, 48], [159, 48]], [[49, 58], [172, 59], [171, 70], [49, 69]], [[772, 58], [909, 58], [909, 65], [772, 65]], [[992, 61], [1061, 61], [1061, 87], [992, 87]], [[1093, 55], [1207, 55], [1207, 65], [1093, 65]], [[30, 71], [170, 71], [170, 81], [30, 81]], [[277, 68], [662, 68], [662, 95], [277, 95]], [[770, 69], [910, 69], [910, 79], [770, 79]], [[1075, 67], [1207, 67], [1207, 77], [1075, 77]], [[31, 85], [169, 85], [169, 92], [31, 92]], [[771, 82], [909, 82], [909, 89], [771, 89]], [[1075, 79], [1207, 79], [1207, 89], [1075, 89]], [[32, 97], [154, 97], [154, 104], [32, 104]], [[771, 94], [910, 94], [910, 101], [771, 101]], [[924, 89], [955, 89], [955, 430], [924, 430]], [[994, 93], [1206, 93], [1206, 100], [994, 100]], [[51, 108], [169, 108], [169, 115], [51, 115]], [[216, 110], [719, 110], [719, 156], [216, 156]], [[771, 106], [909, 106], [909, 113], [771, 113]], [[993, 104], [1207, 104], [1207, 114], [993, 114]], [[31, 119], [170, 119], [170, 129], [31, 129]], [[770, 117], [910, 117], [910, 127], [770, 127]], [[993, 115], [1207, 115], [1207, 125], [993, 125]], [[31, 131], [170, 131], [170, 140], [31, 140]], [[770, 128], [910, 128], [910, 137], [770, 137]], [[964, 131], [984, 131], [984, 478], [964, 478]], [[993, 127], [1207, 127], [1207, 136], [993, 136]], [[31, 141], [170, 141], [170, 151], [31, 151]], [[770, 139], [816, 139], [816, 149], [770, 149]], [[993, 138], [1207, 138], [1207, 148], [993, 148]], [[31, 153], [170, 153], [170, 163], [31, 163]], [[789, 151], [910, 151], [910, 161], [789, 161]], [[993, 149], [1207, 149], [1207, 159], [993, 159]], [[31, 167], [169, 167], [169, 174], [31, 174]], [[252, 162], [687, 162], [687, 186], [252, 186]], [[770, 163], [910, 163], [910, 173], [770, 173]], [[992, 162], [1207, 162], [1207, 172], [992, 172]], [[32, 178], [169, 178], [169, 185], [32, 185]], [[770, 175], [908, 175], [908, 185], [770, 185]], [[992, 173], [1206, 173], [1206, 183], [992, 183]], [[32, 190], [170, 190], [170, 197], [32, 197]], [[771, 188], [909, 188], [909, 195], [771, 195]], [[993, 186], [1037, 186], [1037, 196], [993, 196]], [[32, 202], [169, 202], [169, 209], [32, 209]], [[178, 201], [228, 201], [228, 211], [178, 211]], [[328, 201], [466, 201], [466, 208], [328, 208]], [[476, 201], [613, 201], [613, 208], [476, 208]], [[622, 199], [763, 199], [763, 209], [622, 209]], [[771, 200], [909, 200], [909, 207], [771, 207]], [[1013, 199], [1202, 199], [1202, 206], [1013, 206]], [[30, 212], [49, 212], [49, 222], [30, 222]], [[197, 214], [318, 214], [318, 221], [197, 221]], [[327, 211], [466, 211], [466, 221], [327, 221]], [[476, 211], [614, 211], [614, 221], [476, 221]], [[622, 211], [763, 211], [763, 221], [622, 221]], [[770, 211], [875, 211], [875, 221], [770, 221]], [[993, 210], [1207, 210], [1207, 220], [993, 220]], [[51, 225], [169, 225], [169, 232], [51, 232]], [[179, 225], [317, 225], [317, 232], [179, 232]], [[327, 223], [466, 223], [466, 233], [327, 233]], [[478, 222], [614, 222], [614, 232], [478, 232]], [[622, 222], [762, 222], [762, 232], [622, 232]], [[789, 222], [911, 222], [911, 232], [789, 232]], [[993, 222], [1207, 222], [1207, 231], [993, 231]], [[31, 235], [170, 235], [170, 245], [31, 245]], [[178, 235], [319, 235], [319, 245], [178, 245]], [[328, 236], [463, 236], [463, 243], [328, 243]], [[475, 234], [597, 234], [597, 244], [475, 244]], [[622, 234], [763, 234], [763, 244], [622, 244]], [[770, 234], [910, 234], [910, 244], [770, 244]], [[993, 233], [1207, 233], [1207, 243], [993, 243]], [[31, 247], [169, 247], [169, 257], [31, 257]], [[178, 247], [318, 247], [318, 257], [178, 257]], [[326, 247], [465, 247], [465, 257], [326, 257]], [[495, 248], [613, 248], [613, 255], [495, 255]], [[622, 246], [763, 246], [763, 256], [622, 256]], [[770, 245], [910, 245], [910, 255], [770, 255]], [[993, 244], [1207, 244], [1207, 254], [993, 254]], [[32, 261], [169, 261], [169, 268], [32, 268]], [[178, 259], [318, 259], [318, 269], [178, 269]], [[328, 260], [466, 260], [466, 267], [328, 267]], [[476, 260], [613, 260], [613, 267], [476, 267]], [[623, 259], [762, 259], [762, 266], [623, 266]], [[771, 259], [910, 259], [910, 266], [771, 266]], [[992, 256], [1207, 256], [1207, 266], [992, 266]], [[31, 271], [170, 271], [170, 281], [31, 281]], [[178, 271], [318, 271], [318, 281], [178, 281]], [[328, 272], [467, 272], [467, 279], [328, 279]], [[475, 272], [613, 272], [613, 279], [475, 279]], [[623, 271], [745, 271], [745, 278], [623, 278]], [[771, 271], [909, 271], [909, 278], [771, 278]], [[994, 270], [1206, 270], [1206, 277], [994, 277]], [[32, 284], [169, 284], [169, 291], [32, 291]], [[179, 284], [317, 284], [317, 291], [179, 291]], [[641, 283], [762, 283], [762, 290], [641, 290]], [[771, 283], [909, 283], [909, 290], [771, 290]], [[994, 282], [1203, 282], [1203, 289], [994, 289]], [[32, 296], [169, 296], [169, 303], [32, 303]], [[180, 296], [317, 296], [317, 303], [180, 303]], [[361, 293], [565, 293], [565, 307], [361, 307]], [[623, 295], [762, 295], [762, 302], [623, 302]], [[770, 294], [910, 294], [910, 304], [770, 304]], [[994, 294], [1206, 294], [1206, 301], [994, 301]], [[32, 308], [169, 308], [169, 314], [32, 314]], [[179, 308], [317, 308], [317, 314], [179, 314]], [[622, 306], [763, 306], [763, 315], [622, 315]], [[770, 305], [910, 305], [910, 314], [770, 314]], [[993, 305], [1207, 305], [1207, 314], [993, 314]], [[32, 318], [169, 318], [169, 325], [32, 325]], [[179, 319], [255, 319], [255, 326], [179, 326]], [[342, 319], [606, 319], [606, 342], [342, 342]], [[621, 317], [763, 316], [763, 326], [622, 327]], [[770, 316], [910, 316], [910, 326], [770, 326]], [[992, 316], [1207, 316], [1207, 326], [992, 326]], [[32, 330], [168, 330], [168, 337], [32, 337]], [[196, 329], [318, 329], [318, 339], [196, 339]], [[623, 328], [763, 328], [763, 338], [623, 338]], [[771, 328], [910, 328], [910, 338], [771, 338]], [[993, 326], [1207, 326], [1207, 336], [993, 336]], [[31, 341], [170, 341], [170, 351], [31, 351]], [[178, 341], [318, 341], [318, 351], [178, 351]], [[623, 342], [762, 342], [762, 349], [623, 349]], [[773, 342], [847, 342], [847, 349], [773, 349]], [[31, 353], [171, 353], [171, 363], [31, 363]], [[179, 355], [300, 355], [300, 362], [179, 362]], [[348, 354], [609, 354], [609, 361], [348, 361]], [[623, 354], [762, 354], [762, 361], [623, 361]], [[32, 366], [169, 366], [169, 373], [32, 373]], [[197, 367], [318, 367], [318, 374], [197, 374]], [[326, 364], [611, 364], [611, 374], [326, 374]], [[623, 366], [762, 366], [762, 373], [623, 373]], [[992, 374], [1020, 374], [1020, 385], [992, 385]], [[994, 365], [1206, 365], [1206, 372], [994, 372]], [[32, 378], [170, 378], [170, 385], [32, 385]], [[179, 378], [318, 378], [318, 385], [179, 385]], [[327, 377], [382, 377], [382, 387], [327, 387]], [[622, 376], [644, 376], [644, 388], [622, 388]], [[30, 388], [48, 388], [48, 399], [30, 399]], [[178, 389], [318, 389], [318, 399], [178, 399]], [[347, 390], [613, 390], [613, 397], [347, 397]], [[640, 388], [763, 388], [763, 398], [640, 398]], [[771, 389], [909, 389], [909, 396], [771, 396]], [[1011, 387], [1207, 387], [1207, 397], [1011, 397]], [[50, 399], [171, 399], [171, 409], [50, 409]], [[178, 400], [319, 400], [319, 410], [178, 410]], [[326, 400], [614, 400], [614, 410], [326, 410]], [[622, 399], [763, 399], [763, 409], [622, 409]], [[770, 399], [910, 399], [910, 409], [770, 409]], [[992, 399], [1207, 399], [1207, 409], [992, 409]], [[31, 411], [172, 411], [172, 421], [31, 421]], [[179, 413], [318, 413], [318, 420], [179, 420]], [[326, 412], [450, 412], [450, 422], [326, 422]], [[623, 411], [763, 411], [763, 421], [623, 421]], [[769, 410], [800, 410], [800, 420], [769, 420]], [[992, 410], [1207, 410], [1207, 420], [992, 420]], [[993, 422], [1204, 422], [1204, 432], [993, 432]], [[994, 436], [1206, 436], [1206, 443], [994, 443]], [[79, 449], [415, 449], [415, 472], [79, 472]], [[773, 449], [909, 449], [909, 462], [773, 462]], [[994, 448], [1206, 448], [1206, 455], [994, 455]], [[993, 459], [1115, 459], [1115, 466], [993, 466]], [[773, 472], [910, 472], [910, 485], [773, 485]], [[1013, 471], [1206, 471], [1206, 478], [1013, 478]], [[63, 489], [436, 487], [436, 518], [63, 519]], [[993, 482], [1207, 482], [1207, 491], [993, 491]], [[789, 497], [911, 498], [911, 509], [789, 508]], [[919, 493], [1207, 493], [1207, 503], [919, 503]], [[770, 511], [910, 511], [910, 521], [770, 521]], [[919, 504], [1207, 504], [1207, 514], [919, 514]], [[770, 523], [910, 523], [910, 533], [770, 533]], [[919, 516], [1207, 516], [1207, 526], [919, 526]], [[89, 531], [404, 531], [404, 545], [89, 545]], [[772, 536], [910, 536], [910, 543], [772, 543]], [[920, 530], [1206, 530], [1206, 537], [920, 537]], [[770, 547], [910, 547], [910, 557], [770, 557]], [[920, 542], [1206, 542], [1206, 549], [920, 549]], [[47, 563], [168, 564], [167, 574], [47, 573]], [[771, 558], [820, 558], [820, 568], [771, 568]], [[921, 554], [1206, 554], [1206, 561], [921, 561]], [[27, 574], [169, 576], [168, 585], [27, 584]], [[180, 566], [316, 566], [316, 573], [180, 573]], [[325, 565], [466, 565], [466, 575], [325, 575]], [[789, 571], [911, 571], [911, 580], [789, 580]], [[919, 565], [1195, 565], [1195, 575], [919, 575]], [[176, 576], [283, 576], [283, 586], [176, 586]], [[327, 576], [466, 576], [466, 586], [327, 586]], [[770, 582], [911, 582], [911, 592], [770, 592]], [[941, 576], [1207, 576], [1207, 586], [941, 586]], [[29, 588], [167, 588], [167, 595], [29, 595]], [[196, 588], [317, 588], [317, 598], [196, 598]], [[326, 588], [459, 588], [459, 598], [326, 598]], [[770, 594], [910, 594], [910, 604], [770, 604]], [[920, 586], [1207, 586], [1207, 596], [920, 596]], [[28, 599], [169, 599], [169, 609], [28, 609]], [[176, 600], [316, 600], [316, 610], [176, 610]], [[345, 599], [466, 599], [466, 609], [345, 609]], [[769, 606], [789, 606], [789, 617], [769, 617]], [[919, 598], [1207, 598], [1207, 608], [919, 608]], [[29, 613], [168, 613], [168, 620], [29, 620]], [[177, 613], [315, 613], [315, 620], [177, 620]], [[326, 611], [464, 611], [464, 621], [326, 621]], [[793, 620], [904, 620], [904, 627], [793, 627]], [[919, 611], [1207, 611], [1207, 621], [919, 621]], [[25, 623], [55, 621], [56, 632], [26, 634]], [[177, 625], [316, 625], [316, 632], [177, 632]], [[345, 623], [466, 623], [466, 633], [345, 633]], [[47, 636], [168, 636], [168, 643], [47, 643]], [[177, 636], [314, 636], [314, 643], [177, 643]], [[327, 637], [465, 637], [465, 644], [327, 644]], [[770, 631], [910, 631], [910, 641], [770, 641]], [[769, 642], [788, 642], [788, 653], [769, 653]], [[920, 636], [1206, 636], [1206, 643], [920, 643]], [[29, 648], [168, 648], [168, 655], [29, 655]], [[177, 649], [316, 649], [316, 656], [177, 656]], [[327, 649], [465, 649], [465, 656], [327, 656]], [[29, 660], [165, 660], [165, 666], [29, 666]], [[176, 659], [317, 659], [317, 668], [176, 668]], [[326, 659], [466, 659], [466, 668], [326, 668]], [[495, 662], [762, 662], [762, 671], [495, 671]], [[788, 666], [894, 666], [894, 679], [788, 679]], [[919, 659], [1208, 659], [1208, 668], [919, 668]], [[29, 671], [167, 671], [167, 678], [29, 678]], [[177, 671], [314, 671], [314, 678], [177, 678]], [[326, 670], [469, 670], [469, 680], [326, 680]], [[471, 671], [727, 672], [727, 682], [471, 681]], [[919, 670], [1135, 670], [1135, 680], [919, 680]], [[28, 681], [168, 681], [168, 691], [28, 691]], [[193, 682], [317, 682], [317, 692], [193, 692]], [[326, 682], [415, 682], [415, 692], [326, 692]], [[711, 683], [749, 683], [749, 693], [711, 693]], [[28, 693], [169, 693], [169, 703], [28, 703]], [[176, 694], [317, 694], [317, 704], [176, 704]], [[787, 689], [897, 689], [897, 702], [787, 702]], [[29, 707], [168, 707], [168, 714], [29, 714]], [[176, 706], [316, 706], [316, 716], [176, 716]], [[388, 709], [702, 709], [702, 732], [388, 732]], [[789, 717], [910, 717], [910, 727], [789, 727]], [[942, 709], [1191, 709], [1191, 726], [942, 726]], [[29, 719], [113, 719], [113, 726], [29, 726]], [[177, 719], [316, 719], [316, 726], [177, 726]], [[177, 731], [316, 731], [316, 738], [177, 738]], [[770, 729], [910, 729], [910, 739], [770, 739]], [[29, 742], [167, 742], [167, 749], [29, 749]], [[176, 742], [317, 742], [317, 752], [176, 752]], [[343, 746], [747, 747], [747, 777], [343, 776]], [[770, 741], [911, 741], [911, 751], [770, 751]], [[939, 739], [1207, 739], [1207, 749], [939, 749]], [[28, 753], [168, 753], [168, 762], [28, 762]], [[176, 753], [316, 753], [316, 763], [176, 763]], [[770, 753], [802, 753], [802, 763], [770, 763]], [[920, 751], [977, 751], [977, 760], [920, 760]], [[27, 764], [57, 764], [57, 774], [27, 774]], [[176, 762], [317, 764], [317, 774], [175, 772]], [[792, 764], [907, 764], [907, 774], [792, 774]], [[939, 762], [1207, 762], [1207, 772], [939, 772]], [[47, 776], [169, 776], [169, 786], [47, 786]], [[176, 776], [316, 776], [316, 786], [176, 786]], [[770, 776], [910, 776], [910, 786], [770, 786]], [[921, 776], [1206, 776], [1206, 783], [921, 783]], [[28, 788], [169, 788], [169, 798], [28, 798]], [[175, 787], [317, 788], [317, 798], [175, 797]], [[357, 786], [735, 786], [735, 806], [357, 806]], [[769, 788], [910, 787], [910, 798], [769, 799]], [[920, 788], [1170, 788], [1170, 795], [920, 795]], [[29, 801], [165, 801], [165, 808], [29, 808]], [[177, 802], [316, 802], [316, 809], [177, 809]], [[770, 801], [865, 801], [865, 811], [770, 811]], [[939, 800], [1206, 800], [1206, 807], [939, 807]], [[29, 813], [168, 813], [168, 820], [29, 820]], [[177, 814], [316, 814], [316, 821], [177, 821]], [[789, 812], [912, 812], [912, 822], [789, 822]], [[918, 811], [1207, 811], [1207, 821], [918, 821]], [[29, 825], [167, 825], [167, 832], [29, 832]], [[176, 825], [247, 825], [247, 832], [176, 832]], [[344, 824], [465, 824], [465, 834], [344, 834]], [[474, 825], [614, 825], [614, 835], [474, 835]], [[622, 825], [762, 825], [762, 835], [622, 835]], [[770, 824], [919, 824], [919, 834], [770, 834]], [[918, 823], [968, 823], [968, 833], [918, 833]], [[29, 837], [96, 837], [96, 843], [29, 843]], [[195, 837], [315, 837], [315, 843], [195, 843]], [[325, 836], [465, 836], [465, 845], [325, 845]], [[474, 836], [615, 836], [615, 845], [474, 845]], [[622, 836], [762, 836], [762, 845], [622, 845]], [[47, 848], [168, 848], [168, 855], [47, 855]], [[175, 847], [317, 847], [317, 857], [175, 857]], [[325, 847], [465, 847], [465, 857], [325, 857]], [[474, 847], [614, 847], [614, 857], [474, 857]], [[622, 847], [762, 847], [762, 857], [622, 857]], [[28, 858], [169, 858], [169, 868], [28, 868]], [[325, 858], [462, 858], [462, 868], [325, 868]], [[622, 858], [762, 859], [762, 870], [621, 869]], [[787, 849], [1191, 849], [1191, 880], [787, 880]], [[176, 859], [268, 859], [268, 869], [176, 869]], [[474, 859], [615, 859], [615, 869], [474, 869]], [[28, 870], [169, 870], [169, 880], [28, 880]], [[194, 871], [317, 871], [317, 881], [194, 881]], [[324, 870], [380, 870], [380, 880], [324, 880]], [[474, 871], [614, 871], [614, 881], [474, 881]], [[622, 872], [759, 872], [759, 882], [622, 882]]], "text_det_params": {"limit_side_len": 736, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["人", "1958年6月8日", "星期日", "新华社索亚6日电保加利", "义者联盟针对苏联、其他社会丰义", "社論", "国际民主妇女联合会在维也", "亚共产党代表大会6日下午结束了", "各国兄弟党代表继续在保共七大致词", "国家和世界共产主义运动和工人运", "纳举行的第四届代表大会，经过", "对部长会议主席于哥夫报告的词", "动而发的排诱论调，这些修正丰义", "五天的会议以后，已经胜利闭", "论：发言者一致回意这个报告。", "者企图向土其其人民表明，美国惠", "高高擎起保卫和平的旗帜", "幕，参加这属大会的有来自七十六个国家的代表，", "在6日上午会议上，保共中央", "一致声讨南共现代修正主义", "魔是能们的“朋友”，然而他们的", "们就“保卫生命”、“妇女在社会上执行母亲、劳动者", "政治局委员拉·达米扬诺夫发了", "企图是不会成功的，他们是要枉费", "和公民职责的条件”、“以和平和各国人民友好的精神", "言，他着重指出，保加利亚共产党", "心机的。他们离间不了各国共产党", "国际民主妇联第四届代表大会的成就", "来教育和培养儿童和青年的权利”等三个议题分别进", "的特征是它在思想上对于敌对阶级", "和工人党。", "行了讨论，并且代表全世界妇女和母亲们的共同愿", "的不可调和性以及对伟大的马克思", "6日下午向保共代表大会致祝", "望，打电报给案全理事会主席，呼吁设法制止核武器", "列宁主义思想的无限忠诚，也还谈", "代表大会通过各项决议并选举了中央机构", "词的还有英国共产党、摩洛哥共产", "试验，通过这届大会，全世界妇女再一次显示了她们", "到保加利亚动力工业的成就和速", "党、芬兰共产党、奥地利共产党、", "团结一致为维护和平，保卫妇女权利和儿童南福而斗", "一步迅速发展动力工业的任务以", "丹妻共产党、伊朗人民党、伊拉克", "争的力量。", "及隆低采媒和电力生产成本的间", "大会致祝词。", "使者在阿拉伯国家中不仅竭力使阿", "分裂民族解放运动和碳坏反帝爱国", "场，并且给予了必要的、明确的和", "共产党、苏丹共产党以及其他共产", "自从1953年在哥本哈根召开世界妇女大会以来，", "题。", "蒙古人民革命党中央委员会政", "拉伯共产主义运动脱离开整个共产", "阵线。他们就是这样在阿拉伯世界", "坚决的批评，我们认为，在阶级分", "党和工人党的代表团团长。", "世界局势已经发生了深刻的变化，并且达到了一个新", "在下午会议上，保共中央政治", "治局委员达姆工说，聘们高光地看", "主义运动和使其分化，而且还使民", "“发展”马克思列宁主义的，但", "析和国际形势的分析方面，他们是", "新华社索非亚7日电保加利", "的转折点，以美国为首的帝国主义阵营，正在遣受新", "局委员塔斯科夫就南斯拉夫商业组", "到，蒙古人民革命党、保加利亚共", "性解放运动脱离开反对童国主义，", "是，他们的破坏调动必将失败。", "不正确的，是不符合事实的，特别", "亚共产党第七次代表大会今天上午", "的严重的经济危机的冲击，而爱好和平的社会主义备", "织的特点，批判了南共的修正主义。", "产党、苏联共产党以及其他兄弟共", "主要是反对美帝国主义的斗争的路", "章大利共产党中央委员会书记", "是在苏联的和平倡议得到有效地和", "一致通过了关于保加利亚共产党中", "国的情况却在蒸蒸日上，亚洲、非洲和拉丁美洲的民", "他说：南斯拉夫领导人站在关于", "产党和工人党对南斯拉夫共产主义", "线，他们千方百计地破坏阿拉伯国", "保罗·布法利尼说，我们不赞成南", "有希望地发展的时候，能们所果期", "央委员会的总结报告的决议，代表", "族独立退动继续高漆，法国、意大利、英国、日本等", "国家在这个阶段“消亡”的这种反", "者联盟领导人的观点作出了一致的", "家和苏联之间的友谊，千方百计地", "斯拉夫同志所采取的不正确的立", "的立场对和平事业就更加有害。", "们完全赞同第六次代表大会以来常", "请本主义国家人民维护和平，争取民主的退动也在更", "马克思主义的修正主义立场上：给", "评价，这些观点的目的是反对马思", "阿根廷共产党中央委员会政治", "中央的政治路线与活动，会设还一", "加广泛地开展，苏联单方面停止核武器试险的决定、", "予生产企业为官们自己的产品寻找", "克列宁主义基本原则，反对社会主", "赫鲁晓夫代表苏联最高苏维埃", "局委员塔迪奥利说，阿根廷共产", "政通过了关于中央检查委员会的总", "苏联和东欧备人民民丰国家的和平倡议、中国人民患", "国内市场和国外市场的权利，同时", "隆营，国限共产主义调动和工", "党赞同苏联和其他国家共产党对", "结报告的决议，关于保共七大对发", "愿军的从朝鲜撤退，受到了全世界爱好和平人民的热", "它们完全有自由在自己之间进行", "运动的团结和期固。", "授予达米扬诺夫列宁勋章", "南共钢领草案和南斯拉夫共产主义", "展国民经济的第三个五年计划指示", "烈欢迎，有力地促进了世界和平运动的进展，苏联三", "列的竞争，自发地改变市场价格。", "达姆丁说，我们同你们在一起", "者联盟第七次代表大会上有关这个", "的决议。大会的代表并且对党章作", "个人造卫星的上天，标表着和平民主力量肯定地超过", "毫无疑问，这种在自发势力影响下", "坚决反击现代修正主义否认苏联具", "纲领的发言的批评，阿根廷共产党", "了一些修改和补充。", "经常改变价格的情况首先就影响到", "有世界历更意义的经验的调言。", "新华社索非亚6日电荷鲁晓夫6日下午在保共领导人日夫科夫", "始终忠于和平宣言和它给社会主", "劳动人民的切身利益，并具可以", "利亚和黎巴嫩共产党款书记", "加理夫等陪同下，拜访了正在病中的保加利亚国民议会主席团主席格", "义国家共产党和工人党宣育的支", "胜利。", "人民在争期和平、民主和民族独立的斗争中取得新的", "促成意争、破产和香并财产的情", "哈立德·巴格达什说，现在，世界", "达米扬诺夫。", "持。", "况。", "舆论应当看清楚南斯拉夫修正主义", "在拜会时，赫鲁晓夫把苏联最高苏维埃主席团在达米扬诺夫六十五", "这一天，代表大会宣读了土耳", "祝词的有瑞典，挪盛、以色列、瑞", "但是，以美国为首的帝国主义集团却顺固地企图", "在6日会议上，有许多国家的", "者在阿拉伯问题上所进行的破坏活", "岁寿晨时授予他的列宁助章交给了他，祝贺达米扬诺夫六十五岁寿辰和", "其共产党的贺词。贺词说，土耳其", "士、卢森堡和加章大等国兄弟党的", "阳挡历史潮流的前进，美帝国主义还在继续进行战争", "共产党和工人党的代表向保共代表", "动，见尔格莱德的修正主义集团的", "他在加强保苏友谊方面的贡献。", "共产党强烈地反对南斯拉夫共产主", "代表。", "威胁和准备新的战争，因此，一切爱好和平的人们仍", "须保持高度的警惕，继续不懈地为维护和平而斗争。", "在这种情况下，国际民主妇女联合会第四属代表大会", "杜克洛对伦敦“工人日报”记者说", "印度报纸刊载毛主席在", "以绿亲名义向世界发出的和平呼吁，无疑地是有着后", "大的感召力量和深远的意义的。", "十三陵水库劳动的照片", "在保卫和平、反对战争的斗争中，妇女是一支伸", "法国人民能够制止个人独裁", "大的力量，从国际民主妇女联合会总书记桑蒂的报告", "新华杜新德里7日电印度", "中，我们高兴地看到：最近又有许多国家的妇女组织加入了国际民主妇", "教徒报”6日在第一版上刊登了毛", "女联合会，而这些国家的妇女以前是没有组织起来和没有参加过社会生", "泽东主席和中国共产党中央委员会", "酒的，这不仅标志着某些国家妇女的进一步觉醒和世界妇女的进一步团", "里昂举行反法西斯群众大会", "的委员们在十三陵参加修建水座的", "结，直具也反映了世界和平力量的壮大和加强，国际民丰妇女联合会根", "劳动的照片，德里其他大报也刊登", "捐各国妇女的共同愿望，在发挥妇女对保卫和平的积极作用方面，进行", "新华杜伦款7日电法国共产", "了这张照片。", "了许条有载的工作，我们相信，经过这次大会，在新形转的推动下，全", "党中央爱员会书记杜克洛在接见伦", "（所谓救国）委员会，是按照量索", "法国的第一座法西斯城市”。他号", "孟买“自由新闻”在刊登这张", "世界的妇女将会更加加强团结和友谊，对世界和平作出更大的贡献。", "里尼的法西斯办法建立的。", "召里昂的共和人士提高警惕，在所", "照片时，还刊载了毛泽东主席在“红", "“没有争取和平和民族独立的有效斗争，就不可能争取到妇女和儿", "敦“工人日报”记者时说。“在法", "正如戴高乐在阿尔及尔的演说", "有的企业中成立反法西斯委员会。", "旗”杂志第一期上发表的文章的摘", "童的权利和幸福”，这句话说得很对，中国妇女从切身的经验中深深体", "国存在着足以堵塞法西斯道路和制", "刚刚表明的那样，新政府显示了它", "国民议会议员皮埃尔·戈特和", "要。", "会到这一点的正确，中国妇女珍视自己已经跟得的权利，正在同全国人", "止行使个人权力的社会和政治力", "的本色，它是要求在阿尔及利亚规", "加米耶·瓦兰也在大会上讲了话。", "“德里时代周刊”发表了评论", "民一道，鼓足干劲，为建设解放了的祖国而进行努力，因此特别感到和", "量。*", "续进行战争的最反动的和最富有殖", "掘报纸报道，有三十五个政治", "今天“工人日”登载的这个", "民主义性质的大资本转力的产物。", "团体和工会组织代表参加的蒙拜利", "赞扬中国领导人参加体力劳动的精", "神。", "积，为使母亲和孩子能等永远生调在没有恐惧的世界里而共同奋斗，现", "访问记援引杜克洛的语说，戴高乐", "而在阿尔及利亚继续进行战争是违", "埃市反法西斯委员会通过了一项决", "政府的组成仅仅具有合法的外数。", "反法国真正的利益的，并且带有把", "议。决议说，委员会“将维护它的", "当法国国民议会6月1日讨论授权戴高乐组织政府问题的时候，巴", "对我国改革农具", "在目前的条件下，就有可能制止战争狂人的冒险，为实现妇女的权利和", "它实际上是国尔及尔和国耶佐军事", "战争扩大到整个北非的严重危验。", "团结，并将给予任何侵犯民主自由", "黎的劳动人民不题军警阻挠举行了反对戴高乐上台的示威游行。", "儿童的幸福创造出首要的条件全世界的持久和平。", "政变的产物，同时也是弗林姆兰政", "杜克洛强调指出，法国共产党", "的阴谋以坚决反击”。", "（新华杜）", "府的可耻的投降和社会党总书记摩", "充分认识到自己所离负的责任，它", "印度舆论很感兴趣", "勒叛卖的结果，摩勒已经变成了戴", "已经及时地使得工人阶级和全国人", "阿尔及利亚共产党书记布哈利", "据新华社新德里7日电印度", "匈社会主义工人党举行中央全会", "高乐个人权力的工具。", "民警惕起来，从直创造了让人民动", "员起来保卫共和国的条件，法国共", "报纸登载的关于在北京举行的农具", "国人民来说，戴高乐政府的成立章", "产党的努力已经使得法西斯阴谋无", "指斥戴高乐极端殖民主义政策", "改革展览会的消息引起这里很大的", "新华杜布达佩斯7日电匈牙利杜会主义工人党中央委员会昨天举", "味着保卫共和国的斗争的新阶段的", "法像他们原来计划的那样实现，现", "兴趣。", "行了全体会议。", "开始。", "在法国共产常向全国发出的号召", "“印度时报”最近发表杜论说：", "中央委员会第一书记卡达尔在会议上作了关于经济互助委员会成员", "他说，戴高乐的部长们都是他", "是：在工厂，城市和农村成立成干", "印度“必须学习制造成本低、节省", "国共产党和工人党代表会议、华沙条约缝约国政治协商委员会会议以及", "个人的仆从，周围有这些仆从的戴", "上万的共和国保卫委员会：加紧进", "民族解放阵线拒绝在阿尔及利亚选举", "劳动力、数力比较大的新农具。这", "匈苏两国经济谈判的报告，常中央委员会一政通过了这个报告，", "高乐，对于共和国制度的威胁更大。", "行争取阿尔及利亚的和平、争取满", "正是中国在做的工作”。", "在会课上党中央政治局委员基仕·卡罗伊还报告了发展自牙利国民", "因为他还拥有修改宪法的权力，能", "足劳动人民的要求和争取缓和国际", "在印度，目前最普通的耕地农", "经济三年计划（1958—1960）的指示，中央委员会讨论了并且一致通过", "将拟定旨在给予他个人权力以宪法", "紧张局势的斗争。", "据新华杜7日讯据捷克斯洛", "怎样回答他6月4日的宣言。我们的", "阿巴斯6日到达开罗出席阿尔及利", "具是用装有锐利的钢口刀的木型。", "了这个指示。", "形式的害法草案。", "新华社7日讯据塔斯林巴警", "伐克通讯社开罗7日消息：陶尔及", "人民将进一步加强他们的解放斗", "亚民族解放阵线联络与执行委员会", "杜克洛指出，载高乐分子想利", "7日讯：里昂举行了有三千人参加", "利亚共产党书记拉比·布哈利就戴", "争，因为他们知道，他们可以依靠突", "会议，他对新闻记者说：“民族解", "用所谓救国委员会来搞一个法西斯", "高乐4日在阿尔及尔发表的演讲，", "放阵线不会接受法国强加于阿尔及", "黎人民军在贝鲁特痛击政府军", "的反法西斯群众大会。", "尼斯和摩洛哥兄弟人民的坚决的支", "性质的运动。这种运动在攻击政治", "法国共产党中央委员、国民议", "发表了谈话。", "持和全世界为自由、民主与和平而", "利亚的任何帝国主义的解决办法，", "利亚在上午的会议上还选举了保加", ".3"], "rec_scores": [0.9924556016921997, 0.9994792938232422, 0.9999107718467712, 0.9501543641090393, 0.9314091205596924, 0.9462019205093384, 0.9878186583518982, 0.9770257472991943, 0.9995439052581787, 0.990073025226593, 0.948004424571991, 0.957452118396759, 0.8389901518821716, 0.9822572469711304, 0.8479700088500977, 0.9229632019996643, 0.9994792342185974, 0.8820764422416687, 0.9518793821334839, 0.9990150928497314, 0.8821598887443542, 0.9733724594116211, 0.9751110076904297, 0.9711633324623108, 0.9665975570678711, 0.9281849265098572, 0.9672975540161133, 0.9987166523933411, 0.9399021863937378, 0.9343006014823914, 0.9395562410354614, 0.9814966320991516, 0.9900821447372437, 0.954505205154419, 0.8899316191673279, 0.9042905569076538, 0.9992778301239014, 0.9446150064468384, 0.9608593583106995, 0.9173194766044617, 0.9116758108139038, 0.9064685702323914, 0.9786516427993774, 0.9020892977714539, 0.9285417795181274, 0.9322611093521118, 0.8885907530784607, 0.8944895267486572, 0.8867434859275818, 0.9658117294311523, 0.9088630080223083, 0.9493749141693115, 0.8628958463668823, 0.9696326851844788, 0.989252507686615, 0.9226294159889221, 0.9636471271514893, 0.9888926148414612, 0.9722580909729004, 0.9086859226226807, 0.8414109349250793, 0.9895511269569397, 0.9740225076675415, 0.9557501077651978, 0.90427166223526, 0.9245808720588684, 0.9783149361610413, 0.910102128982544, 0.7701606154441833, 0.9261859655380249, 0.9860392212867737, 0.9930397272109985, 0.9417991638183594, 0.9818679094314575, 0.9438877105712891, 0.9868384599685669, 0.9370478391647339, 0.9552074670791626, 0.9820489883422852, 0.931791365146637, 0.9286043047904968, 0.9808923006057739, 0.869555652141571, 0.9347397685050964, 0.8543162941932678, 0.9108566045761108, 0.889421820640564, 0.9631888270378113, 0.9641358256340027, 0.9222539067268372, 0.8969491124153137, 0.9680615067481995, 0.9235814213752747, 0.9168481230735779, 0.933291494846344, 0.9586708545684814, 0.9581128358840942, 0.8642567992210388, 0.9632095694541931, 0.9718455076217651, 0.9524965286254883, 0.9985529184341431, 0.8814712762832642, 0.9500610828399658, 0.9485960602760315, 0.892419159412384, 0.6992677450180054, 0.9753401875495911, 0.9639771580696106, 0.9286850690841675, 0.9364206194877625, 0.7772793173789978, 0.9979169368743896, 0.8771508932113647, 0.9936012029647827, 0.9672113656997681, 0.842756986618042, 0.9814395308494568, 0.9820894598960876, 0.9253532290458679, 0.9170342087745667, 0.9702144265174866, 0.9619328379631042, 0.8863252401351929, 0.9646865129470825, 0.9908803701400757, 0.9008913040161133, 0.9269687533378601, 0.9271389842033386, 0.9356287121772766, 0.8858104348182678, 0.9657217860221863, 0.932150661945343, 0.915022611618042, 0.9314831495285034, 0.9345031976699829, 0.953136682510376, 0.9229204058647156, 0.8957417011260986, 0.8297604322433472, 0.9539344906806946, 0.9589396715164185, 0.9727707505226135, 0.7534591555595398, 0.9874957799911499, 0.9720967411994934, 0.9256277680397034, 0.9194281101226807, 0.9336658716201782, 0.9541413187980652, 0.9423263669013977, 0.9940448999404907, 0.8869248032569885, 0.971823513507843, 0.9857701659202576, 0.9572510719299316, 0.9402055144309998, 0.9367912411689758, 0.9451507925987244, 0.9939367175102234, 0.997363269329071, 0.9350116848945618, 0.9198786020278931, 0.9985181093215942, 0.9313459992408752, 0.9985366463661194, 0.9346485137939453, 0.9135876297950745, 0.9574357271194458, 0.9059779047966003, 0.8981726169586182, 0.986741840839386, 0.922827422618866, 0.9959115386009216, 0.8526077270507812, 0.8988620638847351, 0.9432021975517273, 0.9346716403961182, 0.8943313956260681, 0.9560427069664001, 0.9133481383323669, 0.8783398866653442, 0.8024700880050659, 0.9686745405197144, 0.957183837890625, 0.9839390516281128, 0.9745656847953796, 0.9473065137863159, 0.9705705642700195, 0.9736897945404053, 0.8111001253128052, 0.9818236827850342, 0.9402908682823181, 0.918166995048523, 0.9060570001602173, 0.8888307809829712, 0.9824203252792358, 0.9776387810707092, 0.9305957555770874, 0.9189156293869019, 0.9874553680419922, 0.9088325500488281, 0.9516059756278992, 0.9732884168624878, 0.9684785008430481, 0.6882929801940918, 0.9441141486167908, 0.9088252782821655, 0.8758293986320496, 0.9168279767036438, 0.9204338192939758, 0.9622247815132141, 0.8290877938270569, 0.8609865307807922, 0.9349156618118286, 0.9554526209831238, 0.9274629354476929, 0.8823915719985962, 0.9751437306404114, 0.9426258206367493, 0.9824792742729187, 0.998182475566864, 0.9621116518974304, 0.7943328022956848, 0.8391958475112915, 0.9642822742462158, 0.8845080733299255, 0.9586446285247803, 0.9851391911506653, 0.9732886552810669, 0.9484624862670898, 0.831697940826416, 0.9391304850578308, 0.9222191572189331, 0.9815501570701599, 0.9013515710830688, 0.9856187701225281, 0.9996032118797302, 0.9887514114379883, 0.9965471625328064, 0.959753692150116, 0.8965495824813843, 0.9141244292259216, 0.9442067742347717, 0.9330427646636963, 0.9240469336509705, 0.9939900636672974, 0.9731906652450562, 0.9377521276473999, 0.9839100241661072, 0.9576157927513123, 0.6733061671257019, 0.985846996307373, 0.8946320414543152, 0.9161256551742554, 0.9395177960395813, 0.981887936592102, 0.9476245641708374, 0.9321633577346802, 0.9474186301231384, 0.9001193046569824, 0.9395741820335388, 0.8917626738548279, 0.995142936706543, 0.8766562342643738, 0.90682452917099, 0.9396440982818604, 0.8681604266166687, 0.9629509449005127, 0.8959017992019653, 0.962576150894165, 0.9058077335357666, 0.9817887544631958, 0.9688640236854553, 0.9045097231864929, 0.9161633253097534, 0.9253671169281006, 0.9477565288543701, 0.991885244846344, 0.9573789238929749, 0.9857695698738098, 0.864922285079956, 0.7175121903419495, 0.9532672762870789, 0.9812253713607788, 0.9535664319992065, 0.6728801131248474, 0.9842156767845154, 0.9666394591331482, 0.9592775702476501, 0.9305736422538757, 0.9348050355911255, 0.9104968905448914, 0.8864942789077759, 0.9994879364967346, 0.9672743082046509, 0.9566253423690796, 0.9169759154319763, 0.9827023148536682, 0.9685144424438477, 0.9597766995429993, 0.9815586805343628, 0.9495455026626587, 0.9258372187614441], "rec_polys": [[[531, 22], [562, 22], [562, 45], [531, 45]], [[31, 32], [141, 32], [141, 48], [31, 48]], [[159, 32], [211, 32], [211, 48], [159, 48]], [[49, 58], [172, 59], [171, 70], [49, 69]], [[772, 58], [909, 58], [909, 65], [772, 65]], [[992, 61], [1061, 61], [1061, 87], [992, 87]], [[1093, 55], [1207, 55], [1207, 65], [1093, 65]], [[30, 71], [170, 71], [170, 81], [30, 81]], [[277, 68], [662, 68], [662, 95], [277, 95]], [[770, 69], [910, 69], [910, 79], [770, 79]], [[1075, 67], [1207, 67], [1207, 77], [1075, 77]], [[31, 85], [169, 85], [169, 92], [31, 92]], [[771, 82], [909, 82], [909, 89], [771, 89]], [[1075, 79], [1207, 79], [1207, 89], [1075, 89]], [[32, 97], [154, 97], [154, 104], [32, 104]], [[771, 94], [910, 94], [910, 101], [771, 101]], [[924, 89], [955, 89], [955, 430], [924, 430]], [[994, 93], [1206, 93], [1206, 100], [994, 100]], [[51, 108], [169, 108], [169, 115], [51, 115]], [[216, 110], [719, 110], [719, 156], [216, 156]], [[771, 106], [909, 106], [909, 113], [771, 113]], [[993, 104], [1207, 104], [1207, 114], [993, 114]], [[31, 119], [170, 119], [170, 129], [31, 129]], [[770, 117], [910, 117], [910, 127], [770, 127]], [[993, 115], [1207, 115], [1207, 125], [993, 125]], [[31, 131], [170, 131], [170, 140], [31, 140]], [[770, 128], [910, 128], [910, 137], [770, 137]], [[964, 131], [984, 131], [984, 478], [964, 478]], [[993, 127], [1207, 127], [1207, 136], [993, 136]], [[31, 141], [170, 141], [170, 151], [31, 151]], [[770, 139], [816, 139], [816, 149], [770, 149]], [[993, 138], [1207, 138], [1207, 148], [993, 148]], [[31, 153], [170, 153], [170, 163], [31, 163]], [[789, 151], [910, 151], [910, 161], [789, 161]], [[993, 149], [1207, 149], [1207, 159], [993, 159]], [[31, 167], [169, 167], [169, 174], [31, 174]], [[252, 162], [687, 162], [687, 186], [252, 186]], [[770, 163], [910, 163], [910, 173], [770, 173]], [[992, 162], [1207, 162], [1207, 172], [992, 172]], [[32, 178], [169, 178], [169, 185], [32, 185]], [[770, 175], [908, 175], [908, 185], [770, 185]], [[992, 173], [1206, 173], [1206, 183], [992, 183]], [[32, 190], [170, 190], [170, 197], [32, 197]], [[771, 188], [909, 188], [909, 195], [771, 195]], [[993, 186], [1037, 186], [1037, 196], [993, 196]], [[32, 202], [169, 202], [169, 209], [32, 209]], [[178, 201], [228, 201], [228, 211], [178, 211]], [[328, 201], [466, 201], [466, 208], [328, 208]], [[476, 201], [613, 201], [613, 208], [476, 208]], [[622, 199], [763, 199], [763, 209], [622, 209]], [[771, 200], [909, 200], [909, 207], [771, 207]], [[1013, 199], [1202, 199], [1202, 206], [1013, 206]], [[30, 212], [49, 212], [49, 222], [30, 222]], [[197, 214], [318, 214], [318, 221], [197, 221]], [[327, 211], [466, 211], [466, 221], [327, 221]], [[476, 211], [614, 211], [614, 221], [476, 221]], [[622, 211], [763, 211], [763, 221], [622, 221]], [[770, 211], [875, 211], [875, 221], [770, 221]], [[993, 210], [1207, 210], [1207, 220], [993, 220]], [[51, 225], [169, 225], [169, 232], [51, 232]], [[179, 225], [317, 225], [317, 232], [179, 232]], [[327, 223], [466, 223], [466, 233], [327, 233]], [[478, 222], [614, 222], [614, 232], [478, 232]], [[622, 222], [762, 222], [762, 232], [622, 232]], [[789, 222], [911, 222], [911, 232], [789, 232]], [[993, 222], [1207, 222], [1207, 231], [993, 231]], [[31, 235], [170, 235], [170, 245], [31, 245]], [[178, 235], [319, 235], [319, 245], [178, 245]], [[328, 236], [463, 236], [463, 243], [328, 243]], [[475, 234], [597, 234], [597, 244], [475, 244]], [[622, 234], [763, 234], [763, 244], [622, 244]], [[770, 234], [910, 234], [910, 244], [770, 244]], [[993, 233], [1207, 233], [1207, 243], [993, 243]], [[31, 247], [169, 247], [169, 257], [31, 257]], [[178, 247], [318, 247], [318, 257], [178, 257]], [[326, 247], [465, 247], [465, 257], [326, 257]], [[495, 248], [613, 248], [613, 255], [495, 255]], [[622, 246], [763, 246], [763, 256], [622, 256]], [[770, 245], [910, 245], [910, 255], [770, 255]], [[993, 244], [1207, 244], [1207, 254], [993, 254]], [[32, 261], [169, 261], [169, 268], [32, 268]], [[178, 259], [318, 259], [318, 269], [178, 269]], [[328, 260], [466, 260], [466, 267], [328, 267]], [[476, 260], [613, 260], [613, 267], [476, 267]], [[623, 259], [762, 259], [762, 266], [623, 266]], [[771, 259], [910, 259], [910, 266], [771, 266]], [[992, 256], [1207, 256], [1207, 266], [992, 266]], [[31, 271], [170, 271], [170, 281], [31, 281]], [[178, 271], [318, 271], [318, 281], [178, 281]], [[328, 272], [467, 272], [467, 279], [328, 279]], [[475, 272], [613, 272], [613, 279], [475, 279]], [[623, 271], [745, 271], [745, 278], [623, 278]], [[771, 271], [909, 271], [909, 278], [771, 278]], [[994, 270], [1206, 270], [1206, 277], [994, 277]], [[32, 284], [169, 284], [169, 291], [32, 291]], [[179, 284], [317, 284], [317, 291], [179, 291]], [[641, 283], [762, 283], [762, 290], [641, 290]], [[771, 283], [909, 283], [909, 290], [771, 290]], [[994, 282], [1203, 282], [1203, 289], [994, 289]], [[32, 296], [169, 296], [169, 303], [32, 303]], [[180, 296], [317, 296], [317, 303], [180, 303]], [[361, 293], [565, 293], [565, 307], [361, 307]], [[623, 295], [762, 295], [762, 302], [623, 302]], [[770, 294], [910, 294], [910, 304], [770, 304]], [[994, 294], [1206, 294], [1206, 301], [994, 301]], [[32, 308], [169, 308], [169, 314], [32, 314]], [[179, 308], [317, 308], [317, 314], [179, 314]], [[622, 306], [763, 306], [763, 315], [622, 315]], [[770, 305], [910, 305], [910, 314], [770, 314]], [[993, 305], [1207, 305], [1207, 314], [993, 314]], [[32, 318], [169, 318], [169, 325], [32, 325]], [[179, 319], [255, 319], [255, 326], [179, 326]], [[342, 319], [606, 319], [606, 342], [342, 342]], [[621, 317], [763, 316], [763, 326], [622, 327]], [[770, 316], [910, 316], [910, 326], [770, 326]], [[992, 316], [1207, 316], [1207, 326], [992, 326]], [[32, 330], [168, 330], [168, 337], [32, 337]], [[196, 329], [318, 329], [318, 339], [196, 339]], [[623, 328], [763, 328], [763, 338], [623, 338]], [[771, 328], [910, 328], [910, 338], [771, 338]], [[993, 326], [1207, 326], [1207, 336], [993, 336]], [[31, 341], [170, 341], [170, 351], [31, 351]], [[178, 341], [318, 341], [318, 351], [178, 351]], [[623, 342], [762, 342], [762, 349], [623, 349]], [[773, 342], [847, 342], [847, 349], [773, 349]], [[31, 353], [171, 353], [171, 363], [31, 363]], [[179, 355], [300, 355], [300, 362], [179, 362]], [[348, 354], [609, 354], [609, 361], [348, 361]], [[623, 354], [762, 354], [762, 361], [623, 361]], [[32, 366], [169, 366], [169, 373], [32, 373]], [[197, 367], [318, 367], [318, 374], [197, 374]], [[326, 364], [611, 364], [611, 374], [326, 374]], [[623, 366], [762, 366], [762, 373], [623, 373]], [[992, 374], [1020, 374], [1020, 385], [992, 385]], [[994, 365], [1206, 365], [1206, 372], [994, 372]], [[32, 378], [170, 378], [170, 385], [32, 385]], [[179, 378], [318, 378], [318, 385], [179, 385]], [[327, 377], [382, 377], [382, 387], [327, 387]], [[622, 376], [644, 376], [644, 388], [622, 388]], [[30, 388], [48, 388], [48, 399], [30, 399]], [[178, 389], [318, 389], [318, 399], [178, 399]], [[347, 390], [613, 390], [613, 397], [347, 397]], [[640, 388], [763, 388], [763, 398], [640, 398]], [[771, 389], [909, 389], [909, 396], [771, 396]], [[1011, 387], [1207, 387], [1207, 397], [1011, 397]], [[50, 399], [171, 399], [171, 409], [50, 409]], [[178, 400], [319, 400], [319, 410], [178, 410]], [[326, 400], [614, 400], [614, 410], [326, 410]], [[622, 399], [763, 399], [763, 409], [622, 409]], [[770, 399], [910, 399], [910, 409], [770, 409]], [[992, 399], [1207, 399], [1207, 409], [992, 409]], [[31, 411], [172, 411], [172, 421], [31, 421]], [[179, 413], [318, 413], [318, 420], [179, 420]], [[326, 412], [450, 412], [450, 422], [326, 422]], [[623, 411], [763, 411], [763, 421], [623, 421]], [[769, 410], [800, 410], [800, 420], [769, 420]], [[992, 410], [1207, 410], [1207, 420], [992, 420]], [[993, 422], [1204, 422], [1204, 432], [993, 432]], [[994, 436], [1206, 436], [1206, 443], [994, 443]], [[79, 449], [415, 449], [415, 472], [79, 472]], [[773, 449], [909, 449], [909, 462], [773, 462]], [[994, 448], [1206, 448], [1206, 455], [994, 455]], [[993, 459], [1115, 459], [1115, 466], [993, 466]], [[773, 472], [910, 472], [910, 485], [773, 485]], [[1013, 471], [1206, 471], [1206, 478], [1013, 478]], [[63, 489], [436, 487], [436, 518], [63, 519]], [[993, 482], [1207, 482], [1207, 491], [993, 491]], [[789, 497], [911, 498], [911, 509], [789, 508]], [[919, 493], [1207, 493], [1207, 503], [919, 503]], [[770, 511], [910, 511], [910, 521], [770, 521]], [[919, 504], [1207, 504], [1207, 514], [919, 514]], [[770, 523], [910, 523], [910, 533], [770, 533]], [[919, 516], [1207, 516], [1207, 526], [919, 526]], [[89, 531], [404, 531], [404, 545], [89, 545]], [[772, 536], [910, 536], [910, 543], [772, 543]], [[920, 530], [1206, 530], [1206, 537], [920, 537]], [[770, 547], [910, 547], [910, 557], [770, 557]], [[920, 542], [1206, 542], [1206, 549], [920, 549]], [[47, 563], [168, 564], [167, 574], [47, 573]], [[771, 558], [820, 558], [820, 568], [771, 568]], [[921, 554], [1206, 554], [1206, 561], [921, 561]], [[27, 574], [169, 576], [168, 585], [27, 584]], [[180, 566], [316, 566], [316, 573], [180, 573]], [[325, 565], [466, 565], [466, 575], [325, 575]], [[789, 571], [911, 571], [911, 580], [789, 580]], [[919, 565], [1195, 565], [1195, 575], [919, 575]], [[176, 576], [283, 576], [283, 586], [176, 586]], [[327, 576], [466, 576], [466, 586], [327, 586]], [[770, 582], [911, 582], [911, 592], [770, 592]], [[941, 576], [1207, 576], [1207, 586], [941, 586]], [[29, 588], [167, 588], [167, 595], [29, 595]], [[196, 588], [317, 588], [317, 598], [196, 598]], [[326, 588], [459, 588], [459, 598], [326, 598]], [[770, 594], [910, 594], [910, 604], [770, 604]], [[920, 586], [1207, 586], [1207, 596], [920, 596]], [[28, 599], [169, 599], [169, 609], [28, 609]], [[176, 600], [316, 600], [316, 610], [176, 610]], [[345, 599], [466, 599], [466, 609], [345, 609]], [[769, 606], [789, 606], [789, 617], [769, 617]], [[919, 598], [1207, 598], [1207, 608], [919, 608]], [[29, 613], [168, 613], [168, 620], [29, 620]], [[177, 613], [315, 613], [315, 620], [177, 620]], [[326, 611], [464, 611], [464, 621], [326, 621]], [[793, 620], [904, 620], [904, 627], [793, 627]], [[919, 611], [1207, 611], [1207, 621], [919, 621]], [[25, 623], [55, 621], [56, 632], [26, 634]], [[177, 625], [316, 625], [316, 632], [177, 632]], [[345, 623], [466, 623], [466, 633], [345, 633]], [[47, 636], [168, 636], [168, 643], [47, 643]], [[177, 636], [314, 636], [314, 643], [177, 643]], [[327, 637], [465, 637], [465, 644], [327, 644]], [[770, 631], [910, 631], [910, 641], [770, 641]], [[769, 642], [788, 642], [788, 653], [769, 653]], [[920, 636], [1206, 636], [1206, 643], [920, 643]], [[29, 648], [168, 648], [168, 655], [29, 655]], [[177, 649], [316, 649], [316, 656], [177, 656]], [[327, 649], [465, 649], [465, 656], [327, 656]], [[29, 660], [165, 660], [165, 666], [29, 666]], [[176, 659], [317, 659], [317, 668], [176, 668]], [[326, 659], [466, 659], [466, 668], [326, 668]], [[495, 662], [762, 662], [762, 671], [495, 671]], [[788, 666], [894, 666], [894, 679], [788, 679]], [[919, 659], [1208, 659], [1208, 668], [919, 668]], [[29, 671], [167, 671], [167, 678], [29, 678]], [[177, 671], [314, 671], [314, 678], [177, 678]], [[326, 670], [469, 670], [469, 680], [326, 680]], [[471, 671], [727, 672], [727, 682], [471, 681]], [[919, 670], [1135, 670], [1135, 680], [919, 680]], [[28, 681], [168, 681], [168, 691], [28, 691]], [[193, 682], [317, 682], [317, 692], [193, 692]], [[326, 682], [415, 682], [415, 692], [326, 692]], [[711, 683], [749, 683], [749, 693], [711, 693]], [[28, 693], [169, 693], [169, 703], [28, 703]], [[176, 694], [317, 694], [317, 704], [176, 704]], [[787, 689], [897, 689], [897, 702], [787, 702]], [[29, 707], [168, 707], [168, 714], [29, 714]], [[176, 706], [316, 706], [316, 716], [176, 716]], [[388, 709], [702, 709], [702, 732], [388, 732]], [[789, 717], [910, 717], [910, 727], [789, 727]], [[942, 709], [1191, 709], [1191, 726], [942, 726]], [[29, 719], [113, 719], [113, 726], [29, 726]], [[177, 719], [316, 719], [316, 726], [177, 726]], [[177, 731], [316, 731], [316, 738], [177, 738]], [[770, 729], [910, 729], [910, 739], [770, 739]], [[29, 742], [167, 742], [167, 749], [29, 749]], [[176, 742], [317, 742], [317, 752], [176, 752]], [[343, 746], [747, 747], [747, 777], [343, 776]], [[770, 741], [911, 741], [911, 751], [770, 751]], [[939, 739], [1207, 739], [1207, 749], [939, 749]], [[28, 753], [168, 753], [168, 762], [28, 762]], [[176, 753], [316, 753], [316, 763], [176, 763]], [[770, 753], [802, 753], [802, 763], [770, 763]], [[920, 751], [977, 751], [977, 760], [920, 760]], [[27, 764], [57, 764], [57, 774], [27, 774]], [[176, 762], [317, 764], [317, 774], [175, 772]], [[792, 764], [907, 764], [907, 774], [792, 774]], [[939, 762], [1207, 762], [1207, 772], [939, 772]], [[47, 776], [169, 776], [169, 786], [47, 786]], [[176, 776], [316, 776], [316, 786], [176, 786]], [[770, 776], [910, 776], [910, 786], [770, 786]], [[921, 776], [1206, 776], [1206, 783], [921, 783]], [[28, 788], [169, 788], [169, 798], [28, 798]], [[175, 787], [317, 788], [317, 798], [175, 797]], [[357, 786], [735, 786], [735, 806], [357, 806]], [[769, 788], [910, 787], [910, 798], [769, 799]], [[920, 788], [1170, 788], [1170, 795], [920, 795]], [[29, 801], [165, 801], [165, 808], [29, 808]], [[177, 802], [316, 802], [316, 809], [177, 809]], [[770, 801], [865, 801], [865, 811], [770, 811]], [[939, 800], [1206, 800], [1206, 807], [939, 807]], [[29, 813], [168, 813], [168, 820], [29, 820]], [[177, 814], [316, 814], [316, 821], [177, 821]], [[789, 812], [912, 812], [912, 822], [789, 822]], [[918, 811], [1207, 811], [1207, 821], [918, 821]], [[29, 825], [167, 825], [167, 832], [29, 832]], [[176, 825], [247, 825], [247, 832], [176, 832]], [[344, 824], [465, 824], [465, 834], [344, 834]], [[474, 825], [614, 825], [614, 835], [474, 835]], [[622, 825], [762, 825], [762, 835], [622, 835]], [[770, 824], [919, 824], [919, 834], [770, 834]], [[918, 823], [968, 823], [968, 833], [918, 833]], [[29, 837], [96, 837], [96, 843], [29, 843]], [[195, 837], [315, 837], [315, 843], [195, 843]], [[325, 836], [465, 836], [465, 845], [325, 845]], [[474, 836], [615, 836], [615, 845], [474, 845]], [[622, 836], [762, 836], [762, 845], [622, 845]], [[47, 848], [168, 848], [168, 855], [47, 855]], [[175, 847], [317, 847], [317, 857], [175, 857]], [[325, 847], [465, 847], [465, 857], [325, 857]], [[474, 847], [614, 847], [614, 857], [474, 857]], [[622, 847], [762, 847], [762, 857], [622, 857]], [[28, 858], [169, 858], [169, 868], [28, 868]], [[325, 858], [462, 858], [462, 868], [325, 868]], [[622, 858], [762, 859], [762, 870], [621, 869]], [[787, 849], [1191, 849], [1191, 880], [787, 880]], [[176, 859], [268, 859], [268, 869], [176, 869]], [[474, 859], [615, 859], [615, 869], [474, 869]], [[28, 870], [169, 870], [169, 880], [28, 880]], [[194, 871], [317, 871], [317, 881], [194, 881]], [[324, 870], [380, 870], [380, 880], [324, 880]], [[474, 871], [614, 871], [614, 881], [474, 881]], [[622, 872], [759, 872], [759, 882], [622, 882]], [[769, 352], [909, 352], [909, 371], [769, 371]], [[1153, 30], [1195, 30], [1195, 39], [1153, 39]]], "rec_boxes": [[531.0, 22.0, 562.0, 45.0], [31.0, 32.0, 141.0, 48.0], [159.0, 32.0, 211.0, 48.0], [49.0, 58.0, 172.0, 70.0], [772.0, 58.0, 909.0, 65.0], [992.0, 61.0, 1061.0, 87.0], [1093.0, 55.0, 1207.0, 65.0], [30.0, 71.0, 170.0, 81.0], [277.0, 68.0, 662.0, 95.0], [770.0, 69.0, 910.0, 79.0], [1075.0, 67.0, 1207.0, 77.0], [31.0, 85.0, 169.0, 92.0], [771.0, 82.0, 909.0, 89.0], [1075.0, 79.0, 1207.0, 89.0], [32.0, 97.0, 154.0, 104.0], [771.0, 94.0, 910.0, 101.0], [924.0, 89.0, 955.0, 430.0], [994.0, 93.0, 1206.0, 100.0], [51.0, 108.0, 169.0, 115.0], [216.0, 110.0, 719.0, 156.0], [771.0, 106.0, 909.0, 113.0], [993.0, 104.0, 1207.0, 114.0], [31.0, 119.0, 170.0, 129.0], [770.0, 117.0, 910.0, 127.0], [993.0, 115.0, 1207.0, 125.0], [31.0, 131.0, 170.0, 140.0], [770.0, 128.0, 910.0, 137.0], [964.0, 131.0, 984.0, 478.0], [993.0, 127.0, 1207.0, 136.0], [31.0, 141.0, 170.0, 151.0], [770.0, 139.0, 816.0, 149.0], [993.0, 138.0, 1207.0, 148.0], [31.0, 153.0, 170.0, 163.0], [789.0, 151.0, 910.0, 161.0], [993.0, 149.0, 1207.0, 159.0], [31.0, 167.0, 169.0, 174.0], [252.0, 162.0, 687.0, 186.0], [770.0, 163.0, 910.0, 173.0], [992.0, 162.0, 1207.0, 172.0], [32.0, 178.0, 169.0, 185.0], [770.0, 175.0, 908.0, 185.0], [992.0, 173.0, 1206.0, 183.0], [32.0, 190.0, 170.0, 197.0], [771.0, 188.0, 909.0, 195.0], [993.0, 186.0, 1037.0, 196.0], [32.0, 202.0, 169.0, 209.0], [178.0, 201.0, 228.0, 211.0], [328.0, 201.0, 466.0, 208.0], [476.0, 201.0, 613.0, 208.0], [622.0, 199.0, 763.0, 209.0], [771.0, 200.0, 909.0, 207.0], [1013.0, 199.0, 1202.0, 206.0], [30.0, 212.0, 49.0, 222.0], [197.0, 214.0, 318.0, 221.0], [327.0, 211.0, 466.0, 221.0], [476.0, 211.0, 614.0, 221.0], [622.0, 211.0, 763.0, 221.0], [770.0, 211.0, 875.0, 221.0], [993.0, 210.0, 1207.0, 220.0], [51.0, 225.0, 169.0, 232.0], [179.0, 225.0, 317.0, 232.0], [327.0, 223.0, 466.0, 233.0], [478.0, 222.0, 614.0, 232.0], [622.0, 222.0, 762.0, 232.0], [789.0, 222.0, 911.0, 232.0], [993.0, 222.0, 1207.0, 231.0], [31.0, 235.0, 170.0, 245.0], [178.0, 235.0, 319.0, 245.0], [328.0, 236.0, 463.0, 243.0], [475.0, 234.0, 597.0, 244.0], [622.0, 234.0, 763.0, 244.0], [770.0, 234.0, 910.0, 244.0], [993.0, 233.0, 1207.0, 243.0], [31.0, 247.0, 169.0, 257.0], [178.0, 247.0, 318.0, 257.0], [326.0, 247.0, 465.0, 257.0], [495.0, 248.0, 613.0, 255.0], [622.0, 246.0, 763.0, 256.0], [770.0, 245.0, 910.0, 255.0], [993.0, 244.0, 1207.0, 254.0], [32.0, 261.0, 169.0, 268.0], [178.0, 259.0, 318.0, 269.0], [328.0, 260.0, 466.0, 267.0], [476.0, 260.0, 613.0, 267.0], [623.0, 259.0, 762.0, 266.0], [771.0, 259.0, 910.0, 266.0], [992.0, 256.0, 1207.0, 266.0], [31.0, 271.0, 170.0, 281.0], [178.0, 271.0, 318.0, 281.0], [328.0, 272.0, 467.0, 279.0], [475.0, 272.0, 613.0, 279.0], [623.0, 271.0, 745.0, 278.0], [771.0, 271.0, 909.0, 278.0], [994.0, 270.0, 1206.0, 277.0], [32.0, 284.0, 169.0, 291.0], [179.0, 284.0, 317.0, 291.0], [641.0, 283.0, 762.0, 290.0], [771.0, 283.0, 909.0, 290.0], [994.0, 282.0, 1203.0, 289.0], [32.0, 296.0, 169.0, 303.0], [180.0, 296.0, 317.0, 303.0], [361.0, 293.0, 565.0, 307.0], [623.0, 295.0, 762.0, 302.0], [770.0, 294.0, 910.0, 304.0], [994.0, 294.0, 1206.0, 301.0], [32.0, 308.0, 169.0, 314.0], [179.0, 308.0, 317.0, 314.0], [622.0, 306.0, 763.0, 315.0], [770.0, 305.0, 910.0, 314.0], [993.0, 305.0, 1207.0, 314.0], [32.0, 318.0, 169.0, 325.0], [179.0, 319.0, 255.0, 326.0], [342.0, 319.0, 606.0, 342.0], [621.0, 316.0, 763.0, 327.0], [770.0, 316.0, 910.0, 326.0], [992.0, 316.0, 1207.0, 326.0], [32.0, 330.0, 168.0, 337.0], [196.0, 329.0, 318.0, 339.0], [623.0, 328.0, 763.0, 338.0], [771.0, 328.0, 910.0, 338.0], [993.0, 326.0, 1207.0, 336.0], [31.0, 341.0, 170.0, 351.0], [178.0, 341.0, 318.0, 351.0], [623.0, 342.0, 762.0, 349.0], [773.0, 342.0, 847.0, 349.0], [31.0, 353.0, 171.0, 363.0], [179.0, 355.0, 300.0, 362.0], [348.0, 354.0, 609.0, 361.0], [623.0, 354.0, 762.0, 361.0], [32.0, 366.0, 169.0, 373.0], [197.0, 367.0, 318.0, 374.0], [326.0, 364.0, 611.0, 374.0], [623.0, 366.0, 762.0, 373.0], [992.0, 374.0, 1020.0, 385.0], [994.0, 365.0, 1206.0, 372.0], [32.0, 378.0, 170.0, 385.0], [179.0, 378.0, 318.0, 385.0], [327.0, 377.0, 382.0, 387.0], [622.0, 376.0, 644.0, 388.0], [30.0, 388.0, 48.0, 399.0], [178.0, 389.0, 318.0, 399.0], [347.0, 390.0, 613.0, 397.0], [640.0, 388.0, 763.0, 398.0], [771.0, 389.0, 909.0, 396.0], [1011.0, 387.0, 1207.0, 397.0], [50.0, 399.0, 171.0, 409.0], [178.0, 400.0, 319.0, 410.0], [326.0, 400.0, 614.0, 410.0], [622.0, 399.0, 763.0, 409.0], [770.0, 399.0, 910.0, 409.0], [992.0, 399.0, 1207.0, 409.0], [31.0, 411.0, 172.0, 421.0], [179.0, 413.0, 318.0, 420.0], [326.0, 412.0, 450.0, 422.0], [623.0, 411.0, 763.0, 421.0], [769.0, 410.0, 800.0, 420.0], [992.0, 410.0, 1207.0, 420.0], [993.0, 422.0, 1204.0, 432.0], [994.0, 436.0, 1206.0, 443.0], [79.0, 449.0, 415.0, 472.0], [773.0, 449.0, 909.0, 462.0], [994.0, 448.0, 1206.0, 455.0], [993.0, 459.0, 1115.0, 466.0], [773.0, 472.0, 910.0, 485.0], [1013.0, 471.0, 1206.0, 478.0], [63.0, 487.0, 436.0, 519.0], [993.0, 482.0, 1207.0, 491.0], [789.0, 497.0, 911.0, 509.0], [919.0, 493.0, 1207.0, 503.0], [770.0, 511.0, 910.0, 521.0], [919.0, 504.0, 1207.0, 514.0], [770.0, 523.0, 910.0, 533.0], [919.0, 516.0, 1207.0, 526.0], [89.0, 531.0, 404.0, 545.0], [772.0, 536.0, 910.0, 543.0], [920.0, 530.0, 1206.0, 537.0], [770.0, 547.0, 910.0, 557.0], [920.0, 542.0, 1206.0, 549.0], [47.0, 563.0, 168.0, 574.0], [771.0, 558.0, 820.0, 568.0], [921.0, 554.0, 1206.0, 561.0], [27.0, 574.0, 169.0, 585.0], [180.0, 566.0, 316.0, 573.0], [325.0, 565.0, 466.0, 575.0], [789.0, 571.0, 911.0, 580.0], [919.0, 565.0, 1195.0, 575.0], [176.0, 576.0, 283.0, 586.0], [327.0, 576.0, 466.0, 586.0], [770.0, 582.0, 911.0, 592.0], [941.0, 576.0, 1207.0, 586.0], [29.0, 588.0, 167.0, 595.0], [196.0, 588.0, 317.0, 598.0], [326.0, 588.0, 459.0, 598.0], [770.0, 594.0, 910.0, 604.0], [920.0, 586.0, 1207.0, 596.0], [28.0, 599.0, 169.0, 609.0], [176.0, 600.0, 316.0, 610.0], [345.0, 599.0, 466.0, 609.0], [769.0, 606.0, 789.0, 617.0], [919.0, 598.0, 1207.0, 608.0], [29.0, 613.0, 168.0, 620.0], [177.0, 613.0, 315.0, 620.0], [326.0, 611.0, 464.0, 621.0], [793.0, 620.0, 904.0, 627.0], [919.0, 611.0, 1207.0, 621.0], [25.0, 621.0, 56.0, 634.0], [177.0, 625.0, 316.0, 632.0], [345.0, 623.0, 466.0, 633.0], [47.0, 636.0, 168.0, 643.0], [177.0, 636.0, 314.0, 643.0], [327.0, 637.0, 465.0, 644.0], [770.0, 631.0, 910.0, 641.0], [769.0, 642.0, 788.0, 653.0], [920.0, 636.0, 1206.0, 643.0], [29.0, 648.0, 168.0, 655.0], [177.0, 649.0, 316.0, 656.0], [327.0, 649.0, 465.0, 656.0], [29.0, 660.0, 165.0, 666.0], [176.0, 659.0, 317.0, 668.0], [326.0, 659.0, 466.0, 668.0], [495.0, 662.0, 762.0, 671.0], [788.0, 666.0, 894.0, 679.0], [919.0, 659.0, 1208.0, 668.0], [29.0, 671.0, 167.0, 678.0], [177.0, 671.0, 314.0, 678.0], [326.0, 670.0, 469.0, 680.0], [471.0, 671.0, 727.0, 682.0], [919.0, 670.0, 1135.0, 680.0], [28.0, 681.0, 168.0, 691.0], [193.0, 682.0, 317.0, 692.0], [326.0, 682.0, 415.0, 692.0], [711.0, 683.0, 749.0, 693.0], [28.0, 693.0, 169.0, 703.0], [176.0, 694.0, 317.0, 704.0], [787.0, 689.0, 897.0, 702.0], [29.0, 707.0, 168.0, 714.0], [176.0, 706.0, 316.0, 716.0], [388.0, 709.0, 702.0, 732.0], [789.0, 717.0, 910.0, 727.0], [942.0, 709.0, 1191.0, 726.0], [29.0, 719.0, 113.0, 726.0], [177.0, 719.0, 316.0, 726.0], [177.0, 731.0, 316.0, 738.0], [770.0, 729.0, 910.0, 739.0], [29.0, 742.0, 167.0, 749.0], [176.0, 742.0, 317.0, 752.0], [343.0, 746.0, 747.0, 777.0], [770.0, 741.0, 911.0, 751.0], [939.0, 739.0, 1207.0, 749.0], [28.0, 753.0, 168.0, 762.0], [176.0, 753.0, 316.0, 763.0], [770.0, 753.0, 802.0, 763.0], [920.0, 751.0, 977.0, 760.0], [27.0, 764.0, 57.0, 774.0], [175.0, 762.0, 317.0, 774.0], [792.0, 764.0, 907.0, 774.0], [939.0, 762.0, 1207.0, 772.0], [47.0, 776.0, 169.0, 786.0], [176.0, 776.0, 316.0, 786.0], [770.0, 776.0, 910.0, 786.0], [921.0, 776.0, 1206.0, 783.0], [28.0, 788.0, 169.0, 798.0], [175.0, 787.0, 317.0, 798.0], [357.0, 786.0, 735.0, 806.0], [769.0, 787.0, 910.0, 799.0], [920.0, 788.0, 1170.0, 795.0], [29.0, 801.0, 165.0, 808.0], [177.0, 802.0, 316.0, 809.0], [770.0, 801.0, 865.0, 811.0], [939.0, 800.0, 1206.0, 807.0], [29.0, 813.0, 168.0, 820.0], [177.0, 814.0, 316.0, 821.0], [789.0, 812.0, 912.0, 822.0], [918.0, 811.0, 1207.0, 821.0], [29.0, 825.0, 167.0, 832.0], [176.0, 825.0, 247.0, 832.0], [344.0, 824.0, 465.0, 834.0], [474.0, 825.0, 614.0, 835.0], [622.0, 825.0, 762.0, 835.0], [770.0, 824.0, 919.0, 834.0], [918.0, 823.0, 968.0, 833.0], [29.0, 837.0, 96.0, 843.0], [195.0, 837.0, 315.0, 843.0], [325.0, 836.0, 465.0, 845.0], [474.0, 836.0, 615.0, 845.0], [622.0, 836.0, 762.0, 845.0], [47.0, 848.0, 168.0, 855.0], [175.0, 847.0, 317.0, 857.0], [325.0, 847.0, 465.0, 857.0], [474.0, 847.0, 614.0, 857.0], [622.0, 847.0, 762.0, 857.0], [28.0, 858.0, 169.0, 868.0], [325.0, 858.0, 462.0, 868.0], [621.0, 858.0, 762.0, 870.0], [787.0, 849.0, 1191.0, 880.0], [176.0, 859.0, 268.0, 869.0], [474.0, 859.0, 615.0, 869.0], [28.0, 870.0, 169.0, 880.0], [194.0, 871.0, 317.0, 881.0], [324.0, 870.0, 380.0, 880.0], [474.0, 871.0, 614.0, 881.0], [622.0, 872.0, 759.0, 882.0], [769.4297485351562, 352.58868408203125, 909.8967895507812, 371.95562744140625], [1153.15380859375, 30.955997467041016, 1195.94482421875, 39.87283706665039]]}}