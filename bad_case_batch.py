import os
import json
import cv2
import numpy as np
from paddleocr import PPStructureV3
from typing import Dict, List, Tu<PERSON>
from tqdm import tqdm
import re

class BadCaseDetector:
    def __init__(self, output_dir: str = "output"):
        """
        初始化Bad Case检测器
        
        Args:
            output_dir (str): 输出目录路径
        """
        self.output_dir = output_dir
        self.bad_case_dir = os.path.join(output_dir, "bad_cases")
        self.visual_dir = os.path.join(output_dir, "visual")
        self.json_dir = os.path.join(output_dir, "json")
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.bad_case_dir, exist_ok=True)
        os.makedirs(self.visual_dir, exist_ok=True)
        os.makedirs(self.json_dir, exist_ok=True)
        
        # 初始化PPStructure模型
        self.table_engine = PPStructureV3()
        
        # Bad Case统计
        self.bad_case_stats = {
            "total": 0,
            "bad_cases": [],
            "error_types": {
                "missing_key_fields": 0,
                "content_errors": 0,
                "structure_errors": 0,
                "layout_errors": 0,
                "missed_recognition": 0,
                "false_recognition": 0,
                "format_errors": 0,
                "low_confidence": 0,
                "ocr_errors": 0,
                "logical_contradictions": 0
            }
        }

    def save_structure_res(self, result: Dict, save_path: str):
        """
        保存结构化分析结果到JSON文件

        Args:
            result (Dict): 分析结果
            save_path (str): 保存路径
        """
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存结果到 {save_path} 时出错: {str(e)}")

    def process_file(self, file_path: str):
        """
        处理单个文件
        
        Args:
            file_path (str): 输入文件路径
        """
        try:
            # 获取文件名和扩展名
            file_name = os.path.basename(file_path)
            base_name, ext = os.path.splitext(file_name)
            
            # 处理PDF文件（需要先转换为图片）
            if ext.lower() == '.pdf':
                # 这里简化为直接处理第一页，实际应用中可能需要处理多页
                img = self._convert_pdf_to_image(file_path, page=0)
                if img is None:
                    return
                img_path = os.path.join(self.output_dir, f"{base_name}.jpg")
                cv2.imwrite(img_path, img)
            else:
                img_path = file_path
                img = cv2.imread(img_path)
            
            # 执行文档结构分析
            result = self.table_engine.predict(img_path)
            
            # 保存JSON结果
            json_path = os.path.join(self.json_dir, f"{base_name}.json")
            self.save_structure_res(result, json_path)
            
            # 保存可视化结果
            visual_path = os.path.join(self.visual_dir, f"{base_name}_visual.jpg")
            self._draw_structure_result(img, result, visual_path)
            
            # 检测Bad Case
            is_bad_case, reasons = self._detect_bad_case(img, result)
            
            if is_bad_case:
                # 保存Bad Case信息
                self._save_bad_case(file_path, img, result, reasons)
                
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
    
    def _convert_pdf_to_image(self, pdf_path: str, page: int = 0) -> np.ndarray:
        """
        将PDF文件转换为图像
        
        Args:
            pdf_path (str): PDF文件路径
            page (int): 要转换的页码
            
        Returns:
            np.ndarray: 转换后的图像
        """
        try:
            import fitz  # PyMuPDF
            doc = fitz.open(pdf_path)
            if page >= len(doc):
                return None
                
            pix = doc[page].get_pixmap()
            img = np.frombuffer(pix.samples, dtype=np.uint8).reshape(
                (pix.height, pix.width, pix.n))
            if pix.n == 4:  # 带有alpha通道
                img = cv2.cvtColor(img, cv2.COLOR_RGBA2BGR)
            elif pix.n == 3:  # RGB
                img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
            else:  # 灰度
                img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
                
            return img
        except Exception as e:
            print(f"转换PDF {pdf_path} 时出错: {str(e)}")
            return None
    
    def _draw_structure_result(self, img: np.ndarray, result: Dict, save_path: str):
        """
        自定义可视化函数，替代原来的draw_structure_result
        
        Args:
            img (np.ndarray): 原始图像
            result (Dict): 分析结果
            save_path (str): 保存路径
        """
        try:
            # 创建一个图像副本用于绘制
            vis_img = img.copy()
            
            # 定义不同区域类型的颜色
            colors = {
                'table': (0, 255, 0),    # 绿色 - 表格
                'figure': (255, 0, 0),    # 蓝色 - 图片
                'text': (0, 0, 255),      # 红色 - 文本
                'title': (255, 255, 0),   # 青色 - 标题
                'list': (0, 255, 255),    # 黄色 - 列表
                'other': (128, 128, 128)  # 灰色 - 其他
            }
            
            # 绘制每个区域
            for region in result:
                region_type = region.get('type', 'other').lower()
                color = colors.get(region_type, colors['other'])
                
                # 获取边界框
                bbox = region.get('bbox', [])
                if len(bbox) == 4:
                    x1, y1, x2, y2 = map(int, bbox)
                    # 绘制矩形框
                    cv2.rectangle(vis_img, (x1, y1), (x2, y2), color, 2)
                    # 添加区域类型标签
                    cv2.putText(vis_img, region_type, (x1, y1-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # 保存可视化结果
            cv2.imwrite(save_path, vis_img)
        except Exception as e:
            print(f"保存可视化结果时出错: {str(e)}")
            
    def _detect_bad_case(self, img: np.ndarray, result: Dict) -> Tuple[bool, List[str]]:
        """
        检测Bad Case
        
        Args:
            img (np.ndarray): 原始图像
            result (Dict): 分析结果
            
        Returns:
            Tuple[bool, List[str]]: (是否为Bad Case, 错误原因列表)
        """
        reasons = []
        
        # 1. 检查结果是否为空
        if not result or len(result) == 0:
            reasons.append("结果为空")
            return True, reasons
        
        # 2. 检查关键字段缺失
        # 这里需要根据具体文档类型定义关键字段
        # 示例：检查是否有表格区域
        has_table = any(region['type'].lower() == 'table' for region in result)
        if not has_table and self._contains_table_like_structure(img):
            reasons.append("关键字段缺失: 未检测到明显表格结构")
            self.bad_case_stats["error_types"]["missing_key_fields"] += 1
        
        # 3. 检查置信度过低
        for region in result:
            if 'confidence' in region and float(region['confidence']) < 0.7:
                reasons.append(f"置信度过低: {region.get('type', '未知区域')} 置信度 {region['confidence']}")
                self.bad_case_stats["error_types"]["low_confidence"] += 1
        
        # 4. 检查明显的OCR错误
        ocr_errors = self._check_ocr_errors(result)
        if ocr_errors:
            reasons.extend(ocr_errors)
            self.bad_case_stats["error_types"]["ocr_errors"] += len(ocr_errors)
        
        # 5. 检查格式错误
        format_errors = self._check_format_errors(result)
        if format_errors:
            reasons.extend(format_errors)
            self.bad_case_stats["error_types"]["format_errors"] += len(format_errors)
        
        # 6. 检查逻辑矛盾
        logical_errors = self._check_logical_contradictions(result)
        if logical_errors:
            reasons.extend(logical_errors)
            self.bad_case_stats["error_types"]["logical_contradictions"] += len(logical_errors)
        
        # 7. 检查布局错误
        layout_errors = self._check_layout_errors(img, result)
        if layout_errors:
            reasons.extend(layout_errors)
            self.bad_case_stats["error_types"]["layout_errors"] += len(layout_errors)
        
        return len(reasons) > 0, reasons
    
    def _contains_table_like_structure(self, img: np.ndarray) -> bool:
        """
        简单判断图像中是否包含表格状结构
        
        Args:
            img (np.ndarray): 输入图像
            
        Returns:
            bool: 是否包含表格状结构
        """
        # 这里实现一个简单的基于规则的方法检测表格
        # 实际应用中可以使用更复杂的算法
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)
        
        # 检测水平和垂直线条
        kernel_h = np.ones((1, 30), np.uint8)
        kernel_v = np.ones((30, 1), np.uint8)
        horizontal = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel_h)
        vertical = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel_v)
        
        # 如果有足够多的线条，则认为可能有表格
        return np.sum(horizontal > 0) > 1000 or np.sum(vertical > 0) > 1000
    
    def _check_ocr_errors(self, result: Dict) -> List[str]:
        """
        检查OCR错误
        
        Args:
            result (Dict): 分析结果
            
        Returns:
            List[str]: 错误原因列表
        """
        errors = []
        common_ocr_mistakes = {
            '0': 'O', 'O': '0',
            '1': 'l', 'l': '1',
            '2': 'Z', 'Z': '2',
            '5': 'S', 'S': '5',
            '8': 'B', 'B': '8'
        }
        
        for region in result:
            if 'res' not in region:
                continue
                
            for cell in region['res']:
                if 'text' not in cell:
                    continue
                    
                text = cell['text']
                for char in text:
                    if char in common_ocr_mistakes:
                        errors.append(f"OCR可能错误: '{char}' 可能被误识别为 '{common_ocr_mistakes[char]}'")
        
        return errors
    
    def _check_format_errors(self, result: Dict) -> List[str]:
        """
        检查格式错误
        
        Args:
            result (Dict): 分析结果
            
        Returns:
            List[str]: 错误原因列表
        """
        errors = []
        # 这里可以添加各种格式检查
        # 示例：检查日期格式
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',
            r'\d{2}/\d{2}/\d{4}',
            r'\d{4}年\d{2}月\d{2}日'
        ]
        
        for region in result:
            if 'res' not in region:
                continue
                
            for cell in region['res']:
                if 'text' not in cell:
                    continue
                    
                text = cell['text']
                # 检查看起来像日期但格式不匹配的文本
                if any(c.isdigit() for c in text) and ('年' in text or '月' in text or '日' in text or '-' in text or '/' in text):
                    if not any(re.search(pattern, text) for pattern in date_patterns):
                        errors.append(f"日期格式错误: '{text}' 不符合常见日期格式")
        
        return errors
    
    def _check_logical_contradictions(self, result: Dict) -> List[str]:
        """
        检查逻辑矛盾
        
        Args:
            result (Dict): 分析结果
            
        Returns:
            List[str]: 错误原因列表
        """
        errors = []
        # 这里可以添加各种逻辑检查
        # 示例：检查表格中同一列的数字是否合理
        for region in result:
            if region.get('type', '').lower() != 'table' or 'res' not in region:
                continue
                
            # 按列检查数字
            cols = {}
            for cell in region['res']:
                if 'text' not in cell or 'bbox' not in cell:
                    continue
                    
                col = (cell['bbox'][0] + cell['bbox'][2]) // 2
                text = cell['text']
                
                if col not in cols:
                    cols[col] = []
                cols[col].append(text)
            
            # 检查每列的数字是否合理
            for col, values in cols.items():
                numeric_values = []
                for v in values:
                    try:
                        num = float(v.replace(',', ''))
                        numeric_values.append(num)
                    except ValueError:
                        pass
                
                if len(numeric_values) > 1:
                    max_val = max(numeric_values)
                    min_val = min(numeric_values)
                    if max_val / min_val > 1000:  # 数值范围过大
                        errors.append(f"数值范围异常: 列 {col} 中最大值 {max_val} 是最小值 {min_val} 的 {max_val/min_val:.1f} 倍")
        
        return errors
    
    def _check_layout_errors(self, img: np.ndarray, result: Dict) -> List[str]:
        """
        检查布局错误
        
        Args:
            img (np.ndarray): 原始图像
            result (Dict): 分析结果
            
        Returns:
            List[str]: 错误原因列表
        """
        errors = []
        img_height, _ = img.shape[:2]
        
        # 检查文本块顺序是否合理
        text_regions = [r for r in result if r.get('type', '').lower() == 'text']
        if len(text_regions) > 1:
            # 按Y坐标排序
            sorted_regions = sorted(text_regions, key=lambda x: x['bbox'][1])
            
            # 检查是否有明显的顺序错误
            for i in range(1, len(sorted_regions)):
                prev_region = sorted_regions[i-1]
                curr_region = sorted_regions[i]
                
                # 如果前一个区域的底部Y坐标大于当前区域的顶部Y坐标+阈值，可能有顺序错误
                threshold = img_height * 0.05  # 5%的图像高度作为阈值
                if prev_region['bbox'][3] > curr_region['bbox'][1] + threshold:
                    errors.append(f"布局顺序错误: 区域 {i} 可能应该排在区域 {i-1} 前面")
                    self.bad_case_stats["error_types"]["layout_errors"] += 1
        
        return errors
    
    def _save_bad_case(self, file_path: str, img: np.ndarray, result: Dict, reasons: List[str]):
        """
        保存Bad Case信息
        
        Args:
            file_path (str): 原始文件路径
            img (np.ndarray): 原始图像
            result (Dict): 分析结果
            reasons (List[str]): 错误原因列表
        """
        file_name = os.path.basename(file_path)
        base_name, _ = os.path.splitext(file_name)
        
        # 保存Bad Case图像
        bad_case_img_path = os.path.join(self.bad_case_dir, f"{base_name}_bad_case.jpg")
        cv2.imwrite(bad_case_img_path, img)
        
        # 保存Bad Case信息
        bad_case_info = {
            "file_name": file_name,
            "file_path": file_path,
            "reasons": reasons,
            "result": result
        }
        
        bad_case_json_path = os.path.join(self.bad_case_dir, f"{base_name}_bad_case.json")
        with open(bad_case_json_path, 'w', encoding='utf-8') as f:
            json.dump(bad_case_info, f, ensure_ascii=False, indent=2)
        
        # 更新统计信息
        self.bad_case_stats["total"] += 1
        self.bad_case_stats["bad_cases"].append({
            "file_name": file_name,
            "reasons": reasons
        })
    
    def process_folder(self, folder_path: str):
        """
        处理文件夹中的所有文件
        
        Args:
            folder_path (str): 文件夹路径
        """
        supported_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.pdf']
        
        # 收集所有支持的文件
        files = []
        for root, _, filenames in os.walk(folder_path):
            for filename in filenames:
                ext = os.path.splitext(filename)[1].lower()
                if ext in supported_extensions:
                    files.append(os.path.join(root, filename))
        
        # 处理文件
        for file_path in tqdm(files, desc="处理文件中"):
            self.process_file(file_path)
        
        # 保存统计信息
        stats_path = os.path.join(self.output_dir, "bad_case_stats.json")
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(self.bad_case_stats, f, ensure_ascii=False, indent=2)
        
        print(f"\n处理完成。共处理 {len(files)} 个文件，发现 {self.bad_case_stats['total']} 个Bad Case。")
        print(f"统计信息已保存到 {stats_path}")

# 使用示例
if __name__ == "__main__":
    # 输入文件夹路径
    input_folder = "test_images"
    
    # 输出文件夹路径
    output_folder = "bad_case"
    
    # 创建并运行检测器
    detector = BadCaseDetector(output_folder)
    detector.process_folder(input_folder)