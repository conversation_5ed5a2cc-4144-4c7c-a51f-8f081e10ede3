===========================kl_quant_params===========================
model_name:ch_PP-OCRv3_det_KL
python:python3.7
Global.pretrained_model:null
Global.save_inference_dir:null
infer_model:./inference/PP-OCRv3_mobile_det_infer/
infer_export:deploy/slim/quantization/quant_kl.py -c configs/det/PP-OCRv3/PP-OCRv3_det_cml.yml -o
infer_quant:True
inference:tools/infer/predict_det.py
--use_gpu:False|True
--enable_mkldnn:False
--cpu_threads:6
--rec_batch_num:1
--use_tensorrt:False
--precision:int8
--det_model_dir:
--image_dir:./inference/ch_det_data_50/all-sum-510/
null:null
--benchmark:True
null:null
null:null
