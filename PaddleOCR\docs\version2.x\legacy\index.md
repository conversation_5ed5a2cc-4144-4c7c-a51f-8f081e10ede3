---
typora-copy-images-to: images
comments: true
hide:
  - toc
---

# 历史遗留功能

## 概述

本节介绍了 PaddleOCR 2.x 分支的相关功能和模型。由于 3.x 分支的升级，部分模型和功能与旧分支不再兼容。因此，需要使用或参考旧分支特性的用户可以参考这部分文档。

## PaddleOCR 2.x 分支支持的模型：

* [模型列表](model_list.md)

## PaddleOCR 2.x 分支支持的功能：

* [基于Python预测引擎推理](python_infer.md)
* [基于C++预测引擎推理](cpp_infer.md)
* [Visual Studio 2019 Community CMake 编译指南](windows_vs2019_build.md)
* [服务化部署](paddle_server.md)
* [Android 部署](android_demo.md)
* [Jetson 部署](Jetson_infer.md)
* [端侧部署](lite.md)
* [网页前端部署](paddle_js.md)
* [Paddle2ONNX模型转化与预测](paddle2onnx.md)
* [云上飞桨部署工具](paddle_cloud.md)
* [Benchmark](benchmark.md)
