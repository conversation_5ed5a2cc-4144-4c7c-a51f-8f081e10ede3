{"input_path": "test_images\\PNG_transparency_demonstration_1.png", "page_index": null, "model_settings": {"use_doc_preprocessor": false, "use_seal_recognition": true, "use_table_recognition": true, "use_formula_recognition": true, "use_chart_recognition": false, "use_region_detection": true}, "parsing_res_list": [{"block_label": "image", "block_content": "A ", "block_bbox": [0, 20, 800, 520]}], "layout_det_res": {"input_path": null, "page_index": null, "boxes": [{"cls_id": 1, "label": "image", "score": 0.6291863918304443, "coordinate": [0.4515380859375, 20.99517822265625, 800, 520.802734375]}]}, "overall_ocr_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_preprocessor": false, "use_textline_orientation": true}, "dt_polys": [[[532, 0], [740, 135], [587, 316], [380, 136]], [[38, 139], [236, 0], [416, 115], [218, 319]]], "text_det_params": {"limit_side_len": 736, "limit_type": "min", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.6, "unclip_ratio": 1.5}, "text_type": "general", "textline_orientation_angles": [0, 0], "text_rec_score_thresh": 0.0, "rec_texts": ["A", ""], "rec_scores": [0.052339278161525726, 0.0], "rec_polys": [[[532, 0], [740, 135], [587, 316], [380, 136]], [[38, 139], [236, 0], [416, 115], [218, 319]]], "rec_boxes": [[380, 0, 740, 316], [38, 0, 416, 319]]}}