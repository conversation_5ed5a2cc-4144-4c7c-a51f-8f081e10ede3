# Pipeline Overview

A pipeline is a practical functional implementation composed of one or more modules. Through reasonable module combination and configuration, pipelines can meet the needs of complex application scenarios, such as technological applications like Optical Character Recognition (OCR). Pipelines not only demonstrate the integrated application of basic modules but also support capabilities such as high-performance inference and service-oriented deployment, providing users with higher development efficiency and broader application possibilities.
