[build-system]
requires = ["setuptools==72.1.0", "wheel", "setuptools_scm"]
build-backend = "setuptools.build_meta"

[project]
name = "paddleocr"
# After each version release, the version number needs to be incremented
dynamic = ["version"]
description = "Awesome multilingual OCR and document parsing toolkits based on PaddlePaddle"
authors = [
    {name = "PaddlePaddle", email = "<EMAIL>"},
]
maintainers = [
    {name = "PaddlePaddle", email = "<EMAIL>"},
]
readme = "README.md"
requires-python = ">=3.8"
keywords = [
    "ocr",
    "textdetection",
    "textrecognition",
    "paddleocr",
    "crnn",
    "east",
    "star-net",
    "rosetta",
    "ocrlite",
    "db",
    "chineseocr",
    "chinesetextdetection",
    "chinesetextrecognition",
]
license = {text = "Apache License 2.0"}
classifiers = [
    "Intended Audience :: Developers",
    "Operating System :: OS Independent",
    "Natural Language :: Chinese (Simplified)",
    "Programming Language :: Python :: 3",
    "Topic :: Utilities",
]
dependencies = [
    "paddlex[ocr,ie,multimodal]==3.0.1",
    "PyYAML>=6",
    "typing-extensions>=4.12",
]

[project.urls]
homepage = "https://github.com/PaddlePaddle/PaddleOCR"
documentation = "https://github.com/PaddlePaddle/PaddleOCR/blob/main/README.md"
repository = "https://github.com/PaddlePaddle/PaddleOCR.git"
issues = "https://github.com/PaddlePaddle/PaddleOCR/issues"

[project.scripts]
paddleocr = "paddleocr.__main__:console_entry"

[tool.setuptools.packages.find]
where = ["."]
include = ["paddleocr", "paddleocr.*"]
namespaces = false

[tool.setuptools_scm]
version_scheme = 'release-branch-semver'

[tool.pytest.ini_options]
markers = [
    "resource_intensive: mark a test as resource intensive"
]
addopts = "-m 'not resource_intensive'"
