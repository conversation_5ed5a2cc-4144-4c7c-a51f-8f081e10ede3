# Inference with Python or C++ Prediction Engine

Since the 2.x branch, inference with Python or C++ prediction engines has been a significant feature. This functionality allows users to load OCR-related models and perform inference without installing the wheel package.

Due to differences in pre-processing, post-processing, and concatenation details compared to the wheel package, the inference results may slightly vary, and the two cannot be directly interchanged.

For specific usage instructions, please refer to the following documents:

* [Inference with Python Prediction Engine](../../version2.x/legacy/python_infer.md)
* [Inference with C++ Prediction Engine](../../version2.x/legacy/cpp_infer.md)
* [List of Supported Models](../../version2.x/legacy/model_list_2.x.md)
