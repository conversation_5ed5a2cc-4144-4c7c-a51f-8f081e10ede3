{"version": "1.0", "truncation": null, "padding": null, "added_tokens": [{"id": 0, "special": true, "content": "[PAD]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false}, {"id": 1, "special": true, "content": "[BOS]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false}, {"id": 2, "special": true, "content": "[EOS]", "single_word": false, "lstrip": false, "rstrip": false, "normalized": false}], "normalizer": null, "pre_tokenizer": {"type": "ByteLevel", "add_prefix_space": false, "trim_offsets": true}, "post_processor": null, "decoder": null, "model": {"dropout": null, "unk_token": null, "continuing_subword_prefix": null, "end_of_word_suffix": null, "fuse_unk": false, "vocab": {"[PAD]": 0, "[BOS]": 1, "[EOS]": 2, "!": 3, "\"": 4, "#": 5, "$": 6, "&": 7, "'": 8, "(": 9, ")": 10, "*": 11, "+": 12, ",": 13, "-": 14, ".": 15, "/": 16, "0": 17, "1": 18, "2": 19, "3": 20, "4": 21, "5": 22, "6": 23, "7": 24, "8": 25, "9": 26, ":": 27, ";": 28, "<": 29, "=": 30, ">": 31, "?": 32, "@": 33, "A": 34, "B": 35, "C": 36, "D": 37, "E": 38, "F": 39, "G": 40, "H": 41, "I": 42, "J": 43, "K": 44, "L": 45, "M": 46, "N": 47, "O": 48, "P": 49, "Q": 50, "R": 51, "S": 52, "T": 53, "U": 54, "V": 55, "W": 56, "X": 57, "Y": 58, "Z": 59, "[": 60, "\\": 61, "]": 62, "^": 63, "_": 64, "`": 65, "a": 66, "b": 67, "c": 68, "d": 69, "e": 70, "f": 71, "g": 72, "h": 73, "i": 74, "j": 75, "k": 76, "l": 77, "m": 78, "n": 79, "o": 80, "p": 81, "q": 82, "r": 83, "s": 84, "t": 85, "u": 86, "v": 87, "w": 88, "x": 89, "y": 90, "z": 91, "{": 92, "|": 93, "}": 94, "~": 95, "½": 96, "¿": 97, "ï": 98, "Ċ": 99, "č": 100, "Ġ": 101, "Ġ}": 102, "Ġ{": 103, "Ġ\\": 104, "Ġ_": 105, "Ġ^": 106, "Ġ2": 107, "Ġ)": 108, "Ġ(": 109, "Ġ1": 110, "ra": 111, "Ġ=": 112, "Ġ-": 113, "čĊ": 114, "Ġ,": 115, "fra": 116, "frac": 117, "Ġ+": 118, "ma": 119, "ta": 120, "ig": 121, "Ġ0": 122, "ar": 123, "al": 124, "le": 125, "Ġi": 126, "th": 127, "Ġx": 128, "ft": 129, "igh": 130, "me": 131, "righ": 132, "math": 133, "Ġn": 134, "Ġ.": 135, "Ġ\\,": 136, "in": 137, "ph": 138, "Ġd": 139, "left": 140, "Ġa": 141, "right": 142, "am": 143, "eta": 144, "ti": 145, "Ġm": 146, "mu": 147, "Ġ3": 148, "Ġk": 149, "Ġt": 150, "Ġe": 151, "Ġr": 152, "Ġ&": 153, "Ġc": 154, "Ġp": 155, "si": 156, "rm": 157, "de": 158, "mathrm": 159, "Ġ4": 160, "Ġs": 161, "pr": 162, "Ġ~": 163, "pha": 164, "Ġl": 165, "alpha": 166, "da": 167, "ĠA": 168, "Ġ\\;": 169, "ot": 170, "pi": 171, "par": 172, "tial": 173, "partial": 174, "ime": 175, "prime": 176, "psi": 177, "dot": 178, "Ġj": 179, "Ġb": 180, "Ġf": 181, "lta": 182, "Ġ|": 183, "amma": 184, "bda": 185, "ambda": 186, "phi": 187, "Ġq": 188, "bf": 189, "Ġg": 190, "nu": 191, "Ġz": 192, "ray": 193, "array": 194, "ĠM": 195, "ĠT": 196, "Ġy": 197, "cal": 198, "bar": 199, "ĠN": 200, "igma": 201, "ĠR": 202, "rt": 203, "lambda": 204, "int": 205, "ĠB": 206, "ve": 207, "ng": 208, "qu": 209, "ĠL": 210, "Ġ/": 211, "lo": 212, "beta": 213, "ngle": 214, "Ġu": 215, "delta": 216, "sq": 217, "sqrt": 218, "theta": 219, "Ġ\\\\": 220, "gamma": 221, "Ġ]": 222, "sigma": 223, "ga": 224, "mega": 225, "ĠD": 226, "ĠF": 227, "Ġ[": 228, "ĠS": 229, "mathbf": 230, "su": 231, "ĠP": 232, "lon": 233, "Ġv": 234, "sum": 235, "psilon": 236, "ĠV": 237, "ĠC": 238, "cdot": 239, "epsilon": 240, "at": 241, "hat": 242, "ad": 243, "quad": 244, "Ġh": 245, "ho": 246, "rho": 247, "hi": 248, "to": 249, "ĠE": 250, "la": 251, "ĠH": 252, "lde": 253, "tilde": 254, "ĠQ": 255, "Ġ5": 256, "var": 257, "ĠX": 258, "ĠG": 259, "be": 260, "nd": 261, "omega": 262, "end": 263, "gin": 264, "begin": 265, "tau": 266, "Ġ6": 267, "na": 268, "vec": 269, "ĠI": 270, "Ġo": 271, "rangle": 272, "Ġ*": 273, "De": 274, "Delta": 275, "Gamma": 276, "pe": 277, "fty": 278, "infty": 279, "ĠK": 280, "xi": 281, "Ġ8": 282, "ow": 283, "ĠJ": 284, "ĠU": 285, "row": 286, "tar": 287, "ge": 288, "Phi": 289, "ap": 290, "ĠW": 291, "co": 292, "mes": 293, "times": 294, "sin": 295, "ĠZ": 296, "langle": 297, "ope": 298, "rna": 299, "rato": 300, "operato": 301, "rname": 302, "operatorname": 303, "tarrow": 304, "lin": 305, "line": 306, "varphi": 307, "pm": 308, "rline": 309, "Lambda": 310, "Ġ\\!": 311, "Ġ;": 312, "dots": 313, "cos": 314, "Ġw": 315, "rightarrow": 316, "big": 317, "chi": 318, "ove": 319, "Ġ\\}": 320, "overline": 321, "Ġ7": 322, "ex": 323, "pa": 324, "st": 325, "pro": 326, "qquad": 327, "iv": 328, "equ": 329, "equiv": 330, "ĠO": 331, "ln": 332, "Omega": 333, "ll": 334, "Ġ9": 335, "kap": 336, "kappa": 337, "Big": 338, "Ġ\\{": 339, "dag": 340, "ĠY": 341, "\\{": 342, "varepsilon": 343, "cdots": 344, "Ġ:": 345, "mathcal": 346, "Psi": 347, "Ġ>": 348, "bo": 349, "bol": 350, "Ġ<": 351, "ger": 352, "dagger": 353, "ldots": 354, "ell": 355, "bla": 356, "nabla": 357, "exp": 358, "yle": 359, "style": 360, "zeta": 361, "Sigma": 362, "wi": 363, "wide": 364, "sim": 365, "leq": 366, "Ġ!": 367, "bigg": 368, "mathb": 369, "mathbb": 370, "Ġ\\:": 371, "hbar": 372, "otimes": 373, "bold": 374, "\\}": 375, "mi": 376, "prox": 377, "approx": 378, "Pi": 379, "log": 380, "mid": 381, "sp": 382, "vert": 383, "di": 384, "prod": 385, "per": 386, "perp": 387, "ystyle": 388, "laystyle": 389, "splaystyle": 390, "displaystyle": 391, "meq": 392, "simeq": 393, "ed": 394, "wed": 395, "wedge": 396, "widetilde": 397, "sy": 398, "sym": 399, "symbol": 400, "boldsymbol": 401, "ck": 402, "tex": 403, "text": 404, "ri": 405, "Th": 406, "Theta": 407, "geq": 408, "se": 409, "eq": 410, "nde": 411, "unde": 412, "tan": 413, "sc": 414, "ast": 415, "rc": 416, "set": 417, "pt": 418, "widehat": 419, "ci": 420, "circ": 421, "re": 422, "ript": 423, "script": 424, "underline": 425, "Ġ\\|": 426, "rel": 427, "neq": 428, "sta": 429, "stack": 430, "stackrel": 431, "sinh": 432, "op": 433, "us": 434, "cosh": 435, "Bigg": 436, "ce": 437, "textstyle": 438, "star": 439, "not": 440, "frak": 441, "mathfrak": 442, "mp": 443, "biggr": 444, "lus": 445, "oplus": 446, "vartheta": 447, "biggl": 448, "Bigr": 449, "bra": 450, "Bigl": 451, "fo": 452, "sf": 453, "sub": 454, "subset": 455, "ngrightarrow": 456, "ec": 457, "boldmath": 458, "rall": 459, "forall": 460, "scriptstyle": 461, "ect": 462, "parrow": 463, "uparrow": 464, "bj": 465, "bject": 466, "pto": 467, "propto": 468, "Ġ'": 469, "longrightarrow": 470, "bigl": 471, "bigr": 472, "oint": 473, "ps": 474, "maps": 475, "mapsto": 476, "om": 477, "lle": 478, "\\|": 479, "ddot": 480, "cu": 481, "bin": 482, "binom": 483, "vdots": 484, "angle": 485, "leftrightarrow": 486, "over": 487, "or": 488, "mathsf": 489, "cup": 490, "brace": 491, "no": 492, "arc": 493, "flo": 494, "floor": 495, "tri": 496, "triangle": 497, "Xi": 498, "cot": 499, "bot": 500, "cong": 501, "it": 502, "mbe": 503, "numbe": 504, "nonumbe": 505, "nonumber": 506, "cap": 507, "Righ": 508, "Rightarrow": 509, "ze": 510, "size": 511, "textrm": 512, "ne": 513, "arctan": 514, "ralle": 515, "paralle": 516, "parallel": 517, "cfrac": 518, "Ġ--": 519, "object": 520, "ĠObject": 521, "brack": 522, "sh": 523, "arrow": 524, "own": 525, "varrho": 526, "subseteq": 527, "rbrace": 528, "textbf": 529, "imath": 530, "od": 531, "down": 532, "he": 533, "land": 534, "scriptscriptstyle": 535, "scriptsize": 536, "che": 537, "check": 538, "sla": 539, "overrightarrow": 540, "downarrow": 541, "Biggl": 542, "gg": 543, "nto": 544, "phanto": 545, "phantom": 546, "exi": 547, "hline": 548, "sts": 549, "exists": 550, "Biggr": 551, "bu": 552, "rfloor": 553, "ddots": 554, "io": 555, "iota": 556, "llet": 557, "bullet": 558, "colon": 559, "inus": 560, "Upsilon": 561, "lfloor": 562, "lbrack": 563, "underbrace": 564, "neg": 565, "Im": 566, "mathit": 567, "tin": 568, "tiny": 569, "jmath": 570, "lef": 571, "slash": 572, "vee": 573, "minus": 574, "setminus": 575, "Re": 576, "iint": 577, "leftarrow": 578, "Ve": 579, "Vert": 580, "atop": 581, "sup": 582, "bigcup": 583, "wp": 584, "dim": 585, "sec": 586, "supset": 587, "Lo": 588, "lor": 589, "pmod": 590, "mod": 591, "bigoplus": 592, "il": 593, "bmod": 594, "coth": 595, "Le": 596, "ftrightarrow": 597, "Leftrightarrow": 598, "ngleftrightarrow": 599, "sma": 600, "upsilon": 601, "\\,": 602, "csc": 603, "eph": 604, "aleph": 605, "bigwedge": 606, "arcsin": 607, "small": 608, "odot": 609, "overset": 610, "rbrack": 611, "mit": 612, "lbrace": 613, "li": 614, "arp": 615, "arge": 616, "Ġ\\#": 617, "bre": 618, "textsf": 619, "Longrightarrow": 620, "breve": 621, "em": 622, "yset": 623, "varpi": 624, "ptyset": 625, "emptyset": 626, "ff": 627, "iff": 628, "nt": 629, "er": 630, "lap": 631, "lnot": 632, "dash": 633, "under": 634, "slant": 635, "arg": 636, "underset": 637, "Bo": 638, "Box": 639, "Ġ\"": 640, "spa": 641, "space": 642, "deg": 643, "iiint": 644, "oo": 645, "otnot": 646, "footnot": 647, "arpoo": 648, "footnote": 649, "rlap": 650, "es": 651, "imp": 652, "sb": 653, "te": 654, "bigtriangle": 655, "lies": 656, "implies": 657, "\\;": 658, "ker": 659, "footnotesize": 660, "tharpoo": 661, "up": 662, "acu": 663, "acute": 664, "longleftrightarrow": 665, "eil": 666, "lce": 667, "rceil": 668, "lceil": 669, "vphantom": 670, "en": 671, "thin": 672, "ack": 673, "back": 674, "tt": 675, "backslash": 676, "xrightarrow": 677, "vdash": 678, "top": 679, "rightharpoo": 680, "varsigma": 681, "Longleftrightarrow": 682, "mathop": 683, "large": 684, "bigcap": 685, "leqslant": 686, "Ġ`": 687, "overbrace": 688, "nup": 689, "rightharpoonup": 690, "bigotimes": 691, "triangleq": 692, "Large": 693, "ru": 694, "null": 695, "bigtriangleup": 696, "varno": 697, "thing": 698, "varnothing": 699, "doteq": 700, "Ġ\\_": 701, "overleftarrow": 702, "hf": 703, "bigstar": 704, "enspace": 705, "\\!": 706, "stru": 707, "strut": 708, "ominus": 709, "div": 710, "ond": 711, "amond": 712, "ddagger": 713, "Ġcm": 714, "ni": 715, "sk": 716, "diamond": 717, "rVert": 718, "prot": 719, "protect": 720, "ip": 721, "varDelta": 722, "notin": 723, "skip": 724, "lVert": 725, "Ġ\\/": 726, "dotsc": 727, "ill": 728, "ule": 729, "\\:": 730, "hfill": 731, "krightarrow": 732, "okrightarrow": 733, "hookrightarrow": 734, "sharp": 735, "Vdash": 736, "bigvee": 737, "subsetneq": 738, "supseteq": 739, "Ġ?": 740, "ngmapsto": 741, "longmapsto": 742, "cdotp": 743, "geqslant": 744, "bigtriangledown": 745, "dotsb": 746, "lim": 747, "fl": 748, "triangleleft": 749, "flat": 750, "sl": 751, "box": 752, "Ġ---": 753, "sqcup": 754, "jlim": 755, "ls": 756, "mo": 757, "dels": 758, "ref": 759, "models": 760, "tag": 761, "Pr": 762, "mal": 763, "ou": 764, "llap": 765, "thinspace": 766, "enskip": 767, "Vec": 768, "ebox": 769, "kebox": 770, "nor": 771, "rd": 772, "squ": 773, "vline": 774, "¿½": 775, "ï¿½": 776, "Ġï¿½": 777, "makebox": 778, "surd": 779, "normal": 780, "are": 781, "square": 782, "pou": 783, "mathrel": 784, "varOmega": 785, "nds": 786, "smallsetminus": 787, "pounds": 788, "ns": 789, "ss": 790, "smi": 791, "mathor": 792, "rightlef": 793, "textup": 794, "tharpoons": 795, "smile": 796, "mathord": 797, "rightleftharpoons": 798, "cc": 799, "Ġ\\-": 800, "succ": 801, "ftarrow": 802, "rtimes": 803, "det": 804, "prec": 805, "texttt": 806, "oslash": 807, "Ġ\\&": 808, "arrowvert": 809, "lg": 810, "Ġmm": 811, "inter": 812, "ngleftarrow": 813, "hfil": 814, "intercal": 815, "frow": 816, "Ġ\\*": 817, "frown": 818, "mpe": 819, "Ġpt": 820, "varpro": 821, "searrow": 822, "bumpe": 823, "varprojlim": 824, "bumpeq": 825, "Down": 826, "SS": 827, "cd": 828, "ere": 829, "gcd": 830, "ohe": 831, "tw": 832, "leme": 833, "there": 834, "injlim": 835, "tit": 836, "adrightarrow": 837, "varinjlim": 838, "comp": 839, "textit": 840, "fore": 841, "overleftrightarrow": 842, "Downarrow": 843, "oheadrightarrow": 844, "twoheadrightarrow": 845, "lement": 846, "therefore": 847, "complement": 848, "ca": 849, "thi": 850, "longleftarrow": 851, "bigm": 852, "triangleright": 853, "nearrow": 854, "\\#": 855, "nce": 856, "ral": 857, "cance": 858, "thick": 859, "cancel": 860, "Uparrow": 861, "nat": 862, "ural": 863, "mathstrut": 864, "suit": 865, "bigcirc": 866, "smallskip": 867, "diamondsuit": 868, "normalsize": 869, "natural": 870, "gt": 871, "less": 872, "mathtt": 873, "bigsqcup": 874, "thicksim": 875, "lesssim": 876, "bow": 877, "llde": 878, "tie": 879, "nullde": 880, "miter": 881, "limiter": 882, "kern": 883, "bowtie": 884, "nulldelimiter": 885, "nulldelimiterspace": 886, "Da": 887, "hphantom": 888, "ro": 889, "vDa": 890, "barwedge": 891, "beth": 892, "eqno": 893, "vDash": 894, "AR": 895, "Di": 896, "GE": 897, "LAR": 898, "dskip": 899, "ts": 900, "Ġ@": 901, "medskip": 902, "ndown": 903, "gets": 904, "coprod": 905, "dotsm": 906, "smash": 907, "rightharpoondown": 908, "Diamond": 909, "LARGE": 910, "nrightarrow": 911, "nleftrightarrow": 912, "rsim": 913, "rne": 914, "warrow": 915, "mathc": 916, "corne": 917, "textnormal": 918, "preceq": 919, "gtrsim": 920, "roup": 921, "corner": 922, "Ġ\\[": 923, "Ġ\\]": 924, "mathope": 925, "lefteq": 926, "lose": 927, "varkappa": 928, "Bigm": 929, "Biggm": 930, "mathclose": 931, "mathopen": 932, "lefteqn": 933, "Bar": 934, "Ti": 935, "lr": 936, "swarrow": 937, "uge": 938, "vru": 939, "xleftarrow": 940, "mathnormal": 941, "rightrightarrow": 942, "rightleftarrow": 943, "sqsubseteq": 944, "succeq": 945, "Tilde": 946, "lrcorner": 947, "vrule": 948, "rightrightarrows": 949, "rightleftarrows": 950, "AA": 951, "Hat": 952, "ak": 953, "ble": 954, "dou": 955, "hss": 956, "min": 957, "nright": 958, "nleftarrow": 959, "uph": 960, "wbre": 961, "allo": 962, "side": 963, "sqcap": 964, "hom": 965, "bigodot": 966, "arpoonright": 967, "blebarwedge": 968, "doublebarwedge": 969, "upharpoonright": 970, "wbreak": 971, "allowbreak": 972, "sideset": 973, "--": 974, "Huge": 975, "amal": 976, "do": 977, "fbox": 978, "group": 979, "hskip": 980, "lse": 981, "pprox": 982, "rk": 983, "rgroup": 984, "rapprox": 985, "Ġin": 986, "arrayco": 987, "sure": 988, "varlim": 989, "pmb": 990, "cite": 991, "substack": 992, "leftrightarrows": 993, "supsetneq": 994, "Longleftarrow": 995, "updownarrow": 996, "ensure": 997, "lgroup": 998, "gtrapprox": 999, "amalg": 1000, "lsep": 1001, "arraycolsep": 1002, "ensuremath": 1003, "asym": 1004, "ch": 1005, "dig": 1006, "ddag": 1007, "ew": 1008, "gra": 1009, "gime": 1010, "jo": 1011, "ltimes": 1012, "nleq": 1013, "tch": 1014, "frame": 1015, "max": 1016, "thde": 1017, "inrel": 1018, "ver": 1019, "withde": 1020, "ointop": 1021, "notag": 1022, "smallint": 1023, "skew": 1024, "lims": 1025, "asymp": 1026, "digamma": 1027, "grave": 1028, "gimel": 1029, "joinrel": 1030, "framebox": 1031, "withdelims": 1032, "Ar": 1033, "Rrightarrow": 1034, "ae": 1035, "ag": 1036, "fill": 1037, "hspace": 1038, "huge": 1039, "lq": 1040, "nwarrow": 1041, "wline": 1042, "Ġ14": 1043, "mark": 1044, "led": 1045, "inf": 1046, "inde": 1047, "Ġex": 1048, "pitch": 1049, "dotsi": 1050, "intop": 1051, "rowvert": 1052, "llcorner": 1053, "black": 1054, "leqq": 1055, "biggm": 1056, "approxeq": 1057, "diag": 1058, "textsc": 1059, "textsl": 1060, "circled": 1061, "fork": 1062, "cur": 1063, "newline": 1064, "negthick": 1065, "atopwithdelims": 1066, "Leftarrow": 1067, "footnotemark": 1068, "uplus": 1069, "subsetneqq": 1070, "---": 1071, "varlimsup": 1072, "varliminf": 1073, "verb": 1074, "Arrowvert": 1075, "pitchfork": 1076, "blacksquare": 1077, "diagup": 1078, "negthickspace": 1079, "23": 1080, "25": 1081, "\\-": 1082, "\\/": 1083, "ape": 1084, "ckap": 1085, "dddot": 1086, "erline": 1087, "ever": 1088, "ij": 1089, "ice": 1090, "ly": 1091, "md": 1092, "nda": 1093, "nnu": 1094, "nmid": 1095, "nRightarrow": 1096, "nVdash": 1097, "of": 1098, "off": 1099, "sho": 1100, "spe": 1101, "wr": 1102, "ymath": 1103, "Ġ#": 1104, "Ġ\\'": 1105, "Ġ\\^": 1106, "Ġ10": 1107, "Ġ15": 1108, "mannu": 1109, "igarrow": 1110, "fter": 1111, "meral": 1112, "leftrightharpoo": 1113, "rightsqu": 1114, "def": 1115, "arrayst": 1116, "rtmid": 1117, "interline": 1118, "vearrow": 1119, "ngeq": 1120, "hoice": 1121, "lax": 1122, "varGamma": 1123, "varpropto": 1124, "vartriangle": 1125, "varUpsilon": 1126, "biguplus": 1127, "expa": 1128, "Ġ<$": 1129, "mathbin": 1130, "perca": 1131, "textcircled": 1132, "textmd": 1133, "scsh": 1134, "cial": 1135, "retch": 1136, "relax": 1137, "overwithdelims": 1138, "noinde": 1139, "owns": 1140, "veebar": 1141, "underbar": 1142, "underrightarrow": 1143, "upperca": 1144, "backsimeq": 1145, "trianglelefteq": 1146, "boxtimes": 1147, "boxed": 1148, "preccur": 1149, "thickap": 1150, "root": 1151, "romannu": 1152, "mathchoice": 1153, "index": 1154, "circledcirc": 1155, "curvearrow": 1156, "everymath": 1157, "lyeq": 1158, "ndafter": 1159, "offinterline": 1160, "shortmid": 1161, "special": 1162, "leftrightharpoons": 1163, "rightsquigarrow": 1164, "arraystretch": 1165, "expandafter": 1166, "scshape": 1167, "noindent": 1168, "uppercase": 1169, "preccurlyeq": 1170, "thickapprox": 1171, "romannumeral": 1172, "curvearrowright": 1173, "offinterlineskip": 1174}, "merges": ["Ġ }", "Ġ {", "Ġ \\", "Ġ _", "Ġ ^", "Ġ 2", "Ġ )", "Ġ (", "Ġ 1", "r a", "Ġ =", "Ġ -", "č Ċ", "Ġ ,", "f ra", "fra c", "Ġ +", "m a", "t a", "i g", "Ġ 0", "a r", "a l", "l e", "Ġ i", "t h", "Ġ x", "f t", "ig h", "m e", "r igh", "ma th", "Ġ n", "Ġ .", "Ġ\\ ,", "i n", "p h", "Ġ d", "le ft", "Ġ a", "righ t", "a m", "e ta", "t i", "Ġ m", "m u", "Ġ 3", "Ġ k", "Ġ t", "Ġ e", "Ġ r", "Ġ &", "Ġ c", "Ġ p", "s i", "r m", "d e", "math rm", "Ġ 4", "Ġ s", "p r", "Ġ ~", "ph a", "Ġ l", "al pha", "d a", "Ġ A", "Ġ\\ ;", "o t", "p i", "p ar", "ti al", "par tial", "i me", "pr ime", "p si", "d ot", "Ġ j", "Ġ b", "Ġ f", "l ta", "Ġ |", "am ma", "b da", "am bda", "ph i", "Ġ q", "b f", "Ġ g", "n u", "Ġ z", "ra y", "ar ray", "Ġ M", "Ġ T", "Ġ y", "c al", "b ar", "Ġ N", "ig ma", "Ġ R", "r t", "l ambda", "in t", "Ġ B", "v e", "n g", "q u", "Ġ L", "Ġ /", "l o", "b eta", "ng le", "Ġ u", "de lta", "s q", "sq rt", "th eta", "Ġ\\ \\", "g amma", "Ġ ]", "s igma", "g a", "me ga", "Ġ D", "Ġ F", "Ġ [", "Ġ S", "math bf", "s u", "Ġ P", "lo n", "Ġ v", "su m", "psi lon", "Ġ V", "Ġ C", "c dot", "e psilon", "a t", "h at", "a d", "qu ad", "Ġ h", "h o", "r ho", "h i", "t o", "Ġ E", "l a", "Ġ H", "l de", "ti lde", "Ġ Q", "Ġ 5", "v ar", "Ġ X", "Ġ G", "b e", "n d", "o mega", "e nd", "g in", "be gin", "ta u", "Ġ 6", "n a", "ve c", "Ġ I", "Ġ o", "ra ngle", "Ġ *", "D e", "De lta", "G amma", "p e", "ft y", "in fty", "Ġ K", "x i", "Ġ 8", "o w", "Ġ J", "Ġ U", "r ow", "ta r", "g e", "P hi", "a p", "Ġ W", "c o", "me s", "ti mes", "s in", "Ġ Z", "la ngle", "o pe", "r na", "ra to", "ope rato", "rna me", "operato rname", "tar row", "l in", "lin e", "var phi", "p m", "r line", "L ambda", "Ġ\\ !", "Ġ ;", "dot s", "co s", "Ġ w", "righ tarrow", "b ig", "c hi", "o ve", "Ġ\\ }", "ove rline", "Ġ 7", "e x", "p a", "s t", "pr o", "q quad", "i v", "e qu", "equ iv", "Ġ O", "l n", "O mega", "l l", "Ġ 9", "k ap", "kap pa", "B ig", "Ġ\\ {", "da g", "Ġ Y", "\\ {", "var epsilon", "cdot s", "Ġ :", "math cal", "P si", "Ġ >", "b o", "bo l", "Ġ <", "ge r", "dag ger", "l dots", "e ll", "b la", "na bla", "ex p", "y le", "st yle", "z eta", "S igma", "w i", "wi de", "si m", "le q", "Ġ !", "big g", "math b", "mathb b", "Ġ\\ :", "h bar", "o times", "bol d", "\\ }", "m i", "pro x", "ap prox", "P i", "lo g", "mi d", "s p", "ve rt", "d i", "pro d", "pe r", "per p", "y style", "la ystyle", "sp laystyle", "di splaystyle", "me q", "si meq", "e d", "w ed", "wed ge", "wide tilde", "s y", "sy m", "sym bol", "bold symbol", "c k", "t ex", "tex t", "r i", "T h", "Th eta", "ge q", "s e", "e q", "n de", "u nde", "ta n", "s c", "a st", "r c", "se t", "p t", "wide hat", "c i", "ci rc", "r e", "ri pt", "sc ript", "unde rline", "Ġ\\ |", "re l", "n eq", "s ta", "sta ck", "stack rel", "sin h", "o p", "u s", "cos h", "Big g", "c e", "text style", "s tar", "n ot", "fra k", "math frak", "m p", "bigg r", "l us", "op lus", "var theta", "bigg l", "Big r", "b ra", "Big l", "f o", "s f", "su b", "sub set", "ng rightarrow", "e c", "bold math", "ra ll", "fo rall", "script style", "ec t", "par row", "u parrow", "b j", "bj ect", "p to", "pro pto", "Ġ '", "lo ng<PERSON><PERSON>row", "big l", "big r", "o int", "p s", "ma ps", "maps to", "o m", "l le", "\\ |", "d dot", "c u", "b in", "bin om", "v dots", "a ngle", "left rightarrow", "ove r", "o r", "math sf", "cu p", "bra ce", "n o", "ar c", "f lo", "flo or", "t ri", "tri angle", "X i", "c ot", "b ot", "co ng", "i t", "m be", "nu mbe", "no numbe", "nonumbe r", "c ap", "R igh", "<PERSON><PERSON> tarrow", "z e", "si ze", "text rm", "n e", "arc tan", "ra lle", "pa ralle", "paralle l", "c frac", "Ġ- -", "o bject", "ĠO bject", "bra ck", "s h", "ar row", "ow n", "var rho", "subset eq", "r brace", "text bf", "i math", "o d", "d own", "h e", "la nd", "script scriptstyle", "script size", "c he", "che ck", "s la", "over rightarrow", "down arrow", "Bigg l", "g g", "n to", "pha nto", "phanto m", "e xi", "h line", "st s", "exi sts", "Bigg r", "b u", "r floor", "d dots", "i o", "io ta", "lle t", "bu llet", "co lon", "in us", "U psilon", "l floor", "l brack", "unde rbrace", "ne g", "I m", "math it", "t in", "tin y", "j math", "le f", "sla sh", "ve e", "m inus", "set minus", "R e", "i int", "lef tarrow", "V e", "Ve rt", "at op", "su p", "big cup", "w p", "di m", "se c", "sup set", "L o", "lo r", "pm od", "m od", "big oplus", "i l", "b mod", "co th", "L e", "ft rightarrow", "<PERSON>", "ng left<PERSON><PERSON>row", "s ma", "u psilon", "\\ ,", "c sc", "e ph", "al eph", "big wedge", "arc sin", "sma ll", "o dot", "over set", "r brack", "mi t", "l brace", "l i", "ar p", "ar ge", "Ġ\\ #", "b re", "text sf", "<PERSON>", "bre ve", "e m", "y set", "var pi", "pt yset", "em ptyset", "f f", "i ff", "n t", "e r", "la p", "ln ot", "da sh", "unde r", "sla nt", "ar g", "under set", "B o", "Bo x", "Ġ \"", "s pa", "spa ce", "de g", "i iint", "o o", "ot not", "fo otnot", "arp oo", "footnot e", "r lap", "e s", "i mp", "s b", "t e", "big triangle", "li es", "imp lies", "\\ ;", "k er", "footnote size", "th arpoo", "u p", "a cu", "acu te", "lo ngleftrightarrow", "e il", "l ce", "rc eil", "lce il", "v phantom", "e n", "th in", "a ck", "b ack", "t t", "back slash", "x rightarrow", "v dash", "to p", "righ tharpoo", "var sigma", "<PERSON>", "math op", "l arge", "big cap", "leq slant", "Ġ `", "over brace", "nu p", "rightharpoo nup", "big otimes", "triangle q", "L arge", "r u", "nu ll", "bigtriangle up", "var no", "thin g", "varno thing", "dot eq", "Ġ\\ _", "over leftarrow", "h f", "big star", "en space", "\\ !", "st ru", "stru t", "om inus", "d iv", "o nd", "am ond", "d dagger", "Ġc m", "n i", "s k", "di amond", "r <PERSON><PERSON>", "pr ot", "prot ect", "i p", "var Delta", "not in", "sk ip", "l <PERSON><PERSON>", "Ġ\\ /", "dots c", "i ll", "u le", "\\ :", "hf ill", "k <PERSON>arrow", "o krightarrow", "ho ok<PERSON><PERSON>row", "sh arp", "V dash", "big vee", "subset neq", "supset eq", "Ġ ?", "ng mapsto", "lo ngmapsto", "cdot p", "geq slant", "bigtriangle down", "dots b", "li m", "f l", "triangle left", "fl at", "s l", "bo x", "Ġ-- -", "sq cup", "j lim", "l s", "m o", "de ls", "re f", "mo dels", "ta g", "P r", "ma l", "o u", "l lap", "thin space", "en skip", "V ec", "e box", "k ebox", "n or", "r d", "s qu", "v line", "¿ ½", "ï ¿½", "Ġ ï¿½", "ma kebox", "su rd", "nor mal", "ar e", "squ are", "p ou", "math rel", "var Omega", "nd s", "small setminus", "pou nds", "n s", "s s", "s mi", "math or", "right lef", "text up", "tharpoo ns", "smi le", "mathor d", "rightlef tharpoons", "c c", "Ġ\\ -", "su cc", "f tarrow", "r times", "de t", "pr ec", "text tt", "o slash", "Ġ\\ &", "arrow vert", "l g", "Ġm m", "int er", "ngle ftarrow", "hf il", "inter cal", "f row", "Ġ\\ *", "frow n", "m pe", "Ġp t", "var pro", "se arrow", "bu mpe", "varpro jlim", "bumpe q", "D own", "S S", "c d", "e re", "g cd", "o he", "t w", "le me", "th ere", "in jlim", "ti t", "ad rightarrow", "var injlim", "co mp", "tex tit", "fo re", "over left<PERSON><PERSON><PERSON>", "Down arrow", "ohe adrightarrow", "tw ohead<PERSON><PERSON>row", "leme nt", "there fore", "comp lement", "c a", "th i", "lo ngleftarrow", "big m", "triangle right", "ne arrow", "\\ #", "n ce", "ra l", "ca nce", "thi ck", "cance l", "U parrow", "n at", "u ral", "math strut", "su it", "big circ", "small skip", "diamond suit", "normal size", "nat ural", "g t", "le ss", "math tt", "big sqcup", "thick sim", "less sim", "b ow", "l lde", "ti e", "nu llde", "mit er", "li miter", "ker n", "bow tie", "nullde limiter", "nulldelimiter space", "D a", "h phantom", "r o", "v Da", "bar wedge", "be th", "eq no", "vDa sh", "A R", "D i", "G E", "L AR", "d skip", "t s", "Ġ @", "me dskip", "nd own", "ge ts", "co prod", "dots m", "sma sh", "rightharpoo ndown", "<PERSON> amond", "LAR GE", "n rightarrow", "n leftrightarrow", "r sim", "r ne", "w arrow", "math c", "co rne", "text normal", "prec eq", "gt rsim", "ro up", "corne r", "Ġ\\ [", "Ġ\\ ]", "math ope", "left eq", "lo se", "var kappa", "Big m", "Bigg m", "mathc lose", "mathope n", "lefteq n", "B ar", "T i", "l r", "s warrow", "u ge", "v ru", "x leftarrow", "math normal", "right rightarrow", "right leftarrow", "sq subseteq", "succ eq", "Ti lde", "lr corner", "vru le", "rightrightarrow s", "rightleft<PERSON>row s", "A A", "H at", "a k", "b le", "d ou", "h ss", "m in", "n right", "n leftarrow", "u ph", "w bre", "al lo", "si de", "sq cap", "ho m", "big odot", "arpoo nright", "ble barwedge", "dou blebarwedge", "uph arpoonright", "wbre ak", "allo wbreak", "side set", "- -", "H uge", "a mal", "d o", "f box", "g roup", "h skip", "l se", "p prox", "r k", "r group", "ra pprox", "Ġi n", "array co", "su re", "var lim", "pm b", "ci te", "sub stack", "leftrightarrow s", "supset neq", "<PERSON>", "up downarrow", "en sure", "lg roup", "gt rapprox", "amal g", "lse p", "arrayco lsep", "ensure math", "a sym", "c h", "d ig", "d dag", "e w", "g ra", "g ime", "j o", "l times", "n leq", "t ch", "fra me", "ma x", "th de", "in rel", "ve r", "wi thde", "oint op", "no tag", "small int", "sk ew", "lim s", "asym p", "dig amma", "gra ve", "gime l", "jo inrel", "frame box", "withde lims", "A r", "<PERSON> <PERSON><PERSON>row", "a e", "a g", "f ill", "h space", "h uge", "l q", "n warrow", "w line", "Ġ1 4", "ma rk", "le d", "in f", "in de", "Ġe x", "pi tch", "dot si", "int op", "row vert", "ll corner", "bla ck", "leq q", "bigg m", "approx eq", "di ag", "text sc", "text sl", "circ led", "fo rk", "cu r", "ne wline", "neg thick", "atop withdelims", "<PERSON><PERSON>", "footnote mark", "up lus", "subsetneq q", "-- -", "varlim sup", "varlim inf", "ver b", "<PERSON>r row<PERSON>", "pitch fork", "black square", "diag up", "negthick space", "2 3", "2 5", "\\ -", "\\ /", "a pe", "c kap", "d ddot", "e rline", "e ver", "i j", "i ce", "l y", "m d", "n da", "n nu", "n mid", "n <PERSON><PERSON><PERSON>", "n Vdash", "o f", "o ff", "s ho", "s pe", "w r", "y math", "Ġ #", "Ġ\\ '", "Ġ\\ ^", "Ġ1 0", "Ġ1 5", "ma nnu", "ig arrow", "ft er", "me ral", "left right<PERSON><PERSON>o", "right squ", "de f", "array st", "rt mid", "int erline", "ve arrow", "ng eq", "ho ice", "la x", "var Gamma", "var propto", "var triangle", "var Upsilon", "big uplus", "ex pa", "Ġ< $", "mathb in", "per ca", "text circled", "text md", "sc sh", "ci al", "re tch", "re lax", "over with<PERSON><PERSON>", "no inde", "own s", "vee bar", "under bar", "under rightarrow", "up perca", "back simeq", "triangleleft eq", "box times", "box ed", "prec cur", "thi ckap", "ro ot", "ro mannu", "mathc hoice", "inde x", "circled circ", "cur vearrow", "ever ymath", "ly eq", "nda fter", "off interline", "sho rtmid", "spe cial", "leftrightharpoo ns", "<PERSON><PERSON><PERSON>", "arrayst retch", "expa ndafter", "scsh ape", "noinde nt", "upperca se", "preccur lyeq", "thickap prox", "romannu meral", "curvearrow right", "offinterline skip"]}}