===========================serving_params===========================
model_name:ch_PP-OCRv3
python:python3.7
trans_model:-m paddle_serving_client.convert
--det_dirname:./inference/PP-OCRv3_mobile_det_infer/
--model_filename:inference.pdmodel
--params_filename:inference.pdiparams
--det_serving_server:./deploy/pdserving/ppocr_det_v3_serving/
--det_serving_client:./deploy/pdserving/ppocr_det_v3_client/
--rec_dirname:./inference/PP-OCRv3_mobile_rec_infer/
--rec_serving_server:./deploy/pdserving/ppocr_rec_v3_serving/
--rec_serving_client:./deploy/pdserving/ppocr_rec_v3_client/
serving_dir:./deploy/pdserving
web_service:web_service.py --config=config.yml --opt op.det.concurrency="1" op.rec.concurrency="1"
op.det.local_service_conf.devices:gpu|null
op.det.local_service_conf.use_mkldnn:False
op.det.local_service_conf.thread_num:6
op.det.local_service_conf.use_trt:False
op.det.local_service_conf.precision:fp32
op.det.local_service_conf.model_config:
op.rec.local_service_conf.model_config:
pipline:pipeline_http_client.py
--image_dir:../../doc/imgs/1.jpg
