name: PaddleOCR Code Style Check

on:
  pull_request:
  push:
    branches: ['main', 'release/*']

jobs:
  check-code-style:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        ref: ${{ github.ref }}

    - uses: actions/setup-python@v5
      with:
        python-version: '3.10'

    - name: Cache Python dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install Dependencies for Python
      run: |
        python -m pip install --upgrade pip
        pip install "clang-format==13.0.0"

    - uses: pre-commit/action@v3.0.1
      with:
        extra_args: '--all-files'
